import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { GenreType } from '../types/genre';

@Injectable({
    providedIn: 'root',
})
export class GenreService {
    private apiUrl = environment.apiUrl;
    private serviceUrl = `${this.apiUrl}/gender`;

    constructor(private http: HttpClient) {}

    getGenresByMatch(match:string) {
        return this.http.get<GenreType[]>(`${this.serviceUrl}/search`, { params:{s:match},observe: 'response', responseType: 'json' });
    }
}