import { Component, OnInit } from '@angular/core';
import { CustomCalendarHeaderComponent } from '../../../components/custom-calendar-header/custom-calendar-header.component';
import { FormControl, FormGroup } from '@angular/forms';
import { provideMomentDateAdapter } from '@angular/material-moment-adapter';
import { CARRIERS } from '../../../types/api';
import { SuscriptorsService } from '../../../services/suscriptors-services';
import Chart from 'chart.js/auto';
import { abbreviateNumber } from 'js-abbreviation-number';
import { ReproductionsService } from '../../../services/reproductions-service';
import { SuscriptorsGroupedByPeriod, SuscriptorsResponse } from '../../../types/suscriptor';
import moment, { Moment } from 'moment';
import { ReproductionByPeriod } from '../../../types/reproductions';
import { debounceTime, forkJoin } from 'rxjs';
import { GoogleDataService } from '../../../services/google-data-service';
moment.locale('es');
import 'moment/locale/es';

interface ReproductionAndSuscriptorData {
  reproductionsData:ReproductionByPeriod[];
  suscriptorsData:SuscriptorsGroupedByPeriod[];
  graphTemplate:{[prop:string]:{total_r:number}};
}

const scalesConfig = {  
  y:{
    ticks:{
      callback:(val:any) => `${abbreviateNumber(parseInt(val), 0)}`,
      maxTicksLimit:6,
      font:{
        family:"Visby CF",
        size:12,
        weight:500,
      },
      suggestedMin:0,
      padding:15
    },
    border:{
      display:false,
    },
    grid:{
      display:false
    }
  },
  x:{
    ticks:{
      font:{
        family:"Visby CF",
        size:12,
        weight:500,
      },
      padding:20,
      maxRotation:0,
      autoSkip:false,
    },
    border:{
      display:false,
    },
    grid:{
      display:false
    }
  }
};

const scalesConfigDevice = {
  y:{
    ticks:{
      callback:(val:any) =>  `${abbreviateNumber(parseInt(val),0)}`,
      maxTicksLimit:6,
      font:{
        family:"Visby CF",
        size:12,
        weight:500,
      },
      suggestedMin:0,
    },
    border:{
      display:false,
    },
    grid:{
      display:false
    }
  },
  x:{
    ticks:{
      font:{
        family:"Visby CF",
        size:12,
        weight:500,
      },
      maxRotation:0,
      autoSkip:false,
    },
    border:{
      display:false,
    },
    grid:{
      display:false
    }
  }
}

@Component({
  selector: 'app-overview',
  templateUrl: './overview.component.html',
  styleUrl: './overview.component.css',
  providers: [
    provideMomentDateAdapter({
      parse: {
        dateInput: ['l', 'LL'],
      },
      display: {
        dateInput: 'DD / MM / YYYY',
        monthYearLabel: 'MMM YYYY',
        dateA11yLabel: 'LL',
        monthYearA11yLabel: 'MMMM YYYY',
      },
    }),
  ],
})
export class OverviewComponent implements OnInit {
  asMinDate: Moment;
  maxDate: Moment;
  constructor(
    private suscriptorService:SuscriptorsService,
    private reproductionsService: ReproductionsService,
    private googleDataService: GoogleDataService,
    ) {
    this.maxDate = moment().subtract(1, 'd');
    this.asMinDate = this.overviewSearchForm.controls.initDate.value?.clone().add(1,"d") as Moment;
  }
  loadingSuscriptorsData = true;

  carrierOptions = [
    { label: 'Usuarios Totales', value: null },
    { label: 'Usuarios Movistar', value: CARRIERS.MOV },
    { label: 'Usuarios Digitel', value: CARRIERS.DIG },
  ];
  customHeader = CustomCalendarHeaderComponent;
  public deviceTrafficChart!: Chart;
  public locationTrafficChart!: Chart<"doughnut",number[],string>;
  public suscriptorsChart!: Chart;
  public suscriptionsData!: string[];
  public reproductionsData!: string[];
  public suscriptorsData!:SuscriptorsResponse;
  private formatter = new Intl.NumberFormat('en-US')

  overviewSearchForm = new FormGroup({
    carrierOption: new FormControl<CARRIERS | null>(
      this.carrierOptions[0].value
    ),
    initDate: new FormControl<Moment>(
      moment().subtract(1, 'd').subtract(1, 'month')
    ),
    limitDate: new FormControl<Moment | null>(null),
  });

  public getMinSearchDate() {
    return this.overviewSearchForm.controls.limitDate.value !== null?this.overviewSearchForm.controls.limitDate.value.clone().subtract(1, 'year'):moment().subtract(1,"d").subtract(1, 'y');
  }

  setCarrierOption (val:CARRIERS | null) {
    this.overviewSearchForm.controls.carrierOption.setValue(val);
  }

  qtyFormat(target:number) {
    return this.formatter.format(target);
  }

  getSearchParams(){
    return {
      startDate:
        this.overviewSearchForm.controls.initDate.value?.format(
          'YYYY-MM-DD'
        ),
      endDate:
        this.overviewSearchForm.controls.limitDate.value === null
          ? moment().subtract(1, 'd').format('YYYY-MM-DD')
          : this.overviewSearchForm.controls.limitDate.value.format(
              'YYYY-MM-DD'
            ),
      carrier: this.overviewSearchForm.controls.carrierOption.value,
    };
  }

  updateReproductionsAndSuscriptionsChartData(data:ReproductionAndSuscriptorData) {
    data.reproductionsData.forEach((e) => {
      data.graphTemplate[e._date].total_r = e.total;
    });
    let refLabels: { [prop:string]:string} = {};
    this.suscriptorsChart.data = {
      labels: Object.keys(data.graphTemplate).map((k) => {
        if(refLabels[moment(k).format('MM-YYYY')] !== undefined) {
          return '';
        }
        else {
          const asLabel = moment(k).locale('es').format('MMMM').replace(/^./, (c) => c.toUpperCase());
          refLabels[moment(k).format('MM-YYYY')] = asLabel;
          return asLabel;
        }
      }),
      datasets:[
        {
          label: 'suscriptorsDataset',
          data:data.suscriptorsData.map((e) => e.total),
          fill: false,
          backgroundColor: '#550DC5',
          borderColor: '#550DC5',
          pointHoverBackgroundColor:"#550DC5",
          pointHoverBorderColor:"#550DC5",
        },
        {
          label: 'reproductionsDataset',
          data:Object.entries(data.graphTemplate).map(([k,v]) => v.total_r),
          fill: false,
          backgroundColor: '#1BE2ED',
          borderColor: '#1BE2ED',
          pointHoverBackgroundColor:"#1BE2ED",
          pointHoverBorderColor:"#1BE2ED",
        }
      ]
    }
    this.suscriptorsChart.update();
  }

  getTotalSuscriptors () {
    return Object.entries(this.suscriptorsData).reduce((acc,[k,v]) => acc+v.limit_qty,0);
  }

  getTotalSuscriptorsByMonth (startDate:string,endDate:string,carrier:CARRIERS|null=null,targetDates:string[]) {
    return this.suscriptorService.getSuscriptorsHistory(targetDates.map((e) => ({fecha:e})),carrier);
  }

  getTotalSuscriptorsTendency () {
    return this.getAmmountVariation(Object.entries(this.suscriptorsData).reduce((acc,[k,v]) => acc+v.start_qty,0),this.getTotalSuscriptors());
  }

  getAmmountVariation(baseAmount:number,limitAmount:number) {
    return ((limitAmount - baseAmount)/(baseAmount !==0?baseAmount:1))*100;
  }

  getGraphSchema(monthVariation:number) : moment.unitOfTime.DurationConstructor {
    if(monthVariation <= 6){
      return 'd'
    }
    else if(monthVariation > 6 && monthVariation < 12){
      return 'week'
    }
    else {
      return "month"
    }
  }

  createGraphData (startDate:string,endDate:string) : {graph:{[prop:string]:{total_r:number}},schema:moment.unitOfTime.DurationConstructor,alternGraph:string[]} {
    let baseDate = moment(startDate);
    let limitDate = moment(endDate).add(1,'d');
    const asGraphSchema = this.getGraphSchema(limitDate.diff(baseDate,"month",true));
    let asGraphData : {[prop:string]:{total_r:number}} ={};
    const asDateDiff = limitDate.diff(baseDate,asGraphSchema);
    let asDateDiffPrec = limitDate.diff(baseDate,asGraphSchema,true);
    let asAlternGraph:string[] = [];
    if (asGraphSchema !== "d") {
      asGraphData = Object.fromEntries(
        Array(asDateDiff)
          .fill(undefined)
          .map((e,ind) => [baseDate.add(ind !== 0?1:0, asGraphSchema).format('YYYY-MM-DD'), { total_r: 0 }])
        );
        if(asDateDiffPrec - asDateDiff > 0){
          baseDate.add(limitDate.diff(baseDate,"d")-1, "d");
          asGraphData[baseDate.format('YYYY-MM-DD')] = {total_r:0};
        }
        Object.keys(asGraphData).forEach((e,ind) => {
          if(ind !== 0 && ind !== Object.keys(asGraphData).length-1){
            asAlternGraph.push(moment(e).subtract(1,"d").format('YYYY-MM-DD'));
          }
          else if(ind === Object.keys(asGraphData).length-1){
            const _dateRef = Object.keys(asGraphData)[ind-1];
            asAlternGraph.push(moment(_dateRef).add(1,asGraphSchema).subtract(1,"d").format('YYYY-MM-DD'));
            asAlternGraph.push(endDate);
          }
        });

    }
    else {
      asGraphData = Object.fromEntries(
        Array(asDateDiff)
          .fill(undefined)
          .map((e,ind) => {
            return [baseDate.add(ind !== 0?1:0, asGraphSchema).format('YYYY-MM-DD'), { total_r: 0,dateLimit:baseDate.format('YYYY-MM-DD') }]})
      );
      asAlternGraph = Object.keys(asGraphData);
    }
    return {graph:asGraphData,schema:asGraphSchema,alternGraph:asAlternGraph};
  }

  getOverviewData ({carrier,startDate,endDate}:{carrier:CARRIERS|null,startDate:string,endDate:string}) {
    let asGraphData = this.createGraphData(startDate,endDate);
    forkJoin({
      suscriptionsData: this.suscriptorService.getSuscriptors(startDate,endDate),
      reproductionsGraph: this.reproductionsService.getReproductionsByMonthV2({carrier,startDate,endDate,graph:asGraphData.schema === "d"?null:{periods:Object.keys(asGraphData.graph),schema:asGraphData.schema}}),
      suscriptionsGraph: this.getTotalSuscriptorsByMonth(startDate,endDate,carrier,asGraphData.alternGraph),
      locationTrafficData: this.googleDataService.getLocationTrafficData(startDate,endDate),
      deviceTrafficData: this.googleDataService.getDeviceTrafficData(startDate,endDate),
    }).subscribe({
      next: ({suscriptionsData,reproductionsGraph,suscriptionsGraph,locationTrafficData,deviceTrafficData}) => {
        this.suscriptorsData = suscriptionsData.body as SuscriptorsResponse;
        this.updateReproductionsAndSuscriptionsChartData({
          reproductionsData: reproductionsGraph.body as ReproductionByPeriod[],
          suscriptorsData:
            suscriptionsGraph.body as SuscriptorsGroupedByPeriod[],
          graphTemplate: asGraphData.graph,
        });
        this.updateLocationTrafficData(locationTrafficData.body as {country:string,amount:number}[]);
        this.updateDeviceTrafficData(deviceTrafficData.body as {device:string,amount:string}[]);
        this.loadingSuscriptorsData = false;
      },
      error: (err) => {
        console.log(err);
      }
    });
  }
 
  createSuscriptorsChart(){
    this.suscriptorsChart = new Chart('suscriptorsChart', {
      type: 'line',
      data: {
        labels:[],
        datasets: [],
      },
      options: {
        responsive:true,
        aspectRatio: 3.45,
        interaction:{
          intersect:true,
        },
        scales: scalesConfig,
        plugins: {
          legend: {
            display: false,
          },
          tooltip:{
            backgroundColor:"#fff",
            bodyColor:"#000",
            xAlign:"center",
            yAlign:"bottom",
            displayColors:false,
            position:"nearest",
            borderColor:"rgba(28, 28, 28, 0.25)",
            caretPadding:15,
            borderWidth:0.5,
            callbacks:{
              title:(tooltipItem:any) => ``,
              label:(tooltipItem:any) => {
                const {parsed:{y}} = tooltipItem;
                return `${y}`;
              }
            }
          },
        },
        elements: {
          point: {
            radius:0,
            hoverRadius:6,
            hitRadius:8, 
            borderColor:"#fff",
            backgroundColor:"#fff",
          },
          line: {
            borderWidth: 0.75,
            tension:0.3,
          },
        },
      },
    });
  }

  createDeviceTrafficChart() {
    this.deviceTrafficChart = new Chart('deviceTrafficChart', {
      type: 'bar',
      data: {
        labels:[],
        datasets:[]
      },
      options: {
        events: [],
        scales: scalesConfigDevice,
        aspectRatio: 2.5,
        elements: {
          bar: { 
            borderRadius: 8,
            borderColor: 'transparent',
            borderSkipped: false,
          },
        },
        plugins: {
          legend: {
            display: false,
          },
        },
      },
    });
  }

  updateDeviceTrafficData (data:{device:string,amount:string}[]): void {
    this.deviceTrafficChart.data = {
        labels:data.map((d) => d.device.replace(/^./, (c) => c.toUpperCase())),
        datasets:[
          {
            data:data.map((d) => parseInt(d.amount)),
            backgroundColor: [
              '#6BDCCC',
              '#1C1468',
              '#DF08A8',
            ],
            maxBarThickness:40,
          }
        ]
    }
    this.deviceTrafficChart.update();
  }

  createLocationTrafficChart(){
    this.locationTrafficChart = new Chart<"doughnut",number[],string>('locationTrafficChart', {
      type: 'doughnut',
      data: {
        labels: [],
        datasets: [],
      },
      options: {
        maintainAspectRatio:false,
        responsive: true,
        aspectRatio: 2.5,
        layout:{
          padding:{
            left:0,
            right:20,
            top:30,
            bottom:30,
          },
        },
        elements: {
          arc: {
            borderRadius: 6,
            borderColor:'#fff',
            borderWidth:2,
          },
        },
        plugins: {
          tooltip:{
            bodyColor:"#fff",
            displayColors:false,
            callbacks:{
              title:(tooltipItem:any) => ``,
              label: (tooltipItem) => `${((tooltipItem.parsed/tooltipItem.dataset.data.reduce((acc,curr) =>acc+curr,0))*100).toFixed(2)}%`,
            }
          },
          legend: {
            position:"right",
            align:"center",
            fullSize:true,
            labels: {
              boxHeight:6,
              boxWidth:4,
              padding:18,
              usePointStyle: true,
              font: {
                family: 'Visby CF',
                weight: 500,
                size: 12,
                lineHeight: 18,
              },
              color: '#201F28',
            },
          },
        },
      },
    });
  }

  updateLocationTrafficData(data:{country:string,amount:number}[]): void {
    const globalAmount = data.reduce((acc,curr) => acc+curr.amount,0);
    this.locationTrafficChart.data = {
      labels: data.map((d) => d.country.replace(/^./, (c) => c.toUpperCase())+` `+`${((d.amount/globalAmount)*100).toFixed(2)}%`),
      datasets: [
        {
          data: data.map((d) => d.amount),
          backgroundColor: ['#FFA01B', '#550DC5', '#1BE2ED', '#A8C5DA'],
          hoverOffset: 4,
          spacing: 2.5,
        },
      ],
    }
    this.locationTrafficChart.update();
  }

  ngOnInit() {
    this.createSuscriptorsChart();
    this.overviewSearchForm.controls.initDate.valueChanges.pipe(debounceTime(500)).subscribe((val) => {
      if(val !== null){
        this.asMinDate = val.clone().add(1,"d");
        const nVal = val.clone();
        if(nVal.clone().add(1,"year").isAfter(moment().subtract(1,"d"))){
          this.maxDate = moment().subtract(1,"d");
        } 
        else {
          this.maxDate = nVal.clone().add(1,"year");
        }
      }
    })
    this.createDeviceTrafficChart();
    this.createLocationTrafficChart();
    this.overviewSearchForm.valueChanges.pipe(debounceTime(500)).subscribe(() => {
      this.getOverviewData(this.getSearchParams() as {carrier:CARRIERS|null,startDate:string,endDate:string});
    });
    this.getOverviewData(this.getSearchParams() as {carrier:CARRIERS|null,startDate:string,endDate:string});
  }
}