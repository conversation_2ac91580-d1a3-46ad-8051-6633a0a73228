import { Component, EventEmitter, Input, Output } from '@angular/core';
import { LookupOption } from '../../../../../types/radio';
import { PlatformMediaTypeLabel } from '../../../../../types/platform-media';

@Component({
  selector: 'app-lookup-config-item',
  templateUrl: './lookup-config-item.component.html',
  styleUrl: './lookup-config-item.component.css'
})
export class LookupConfigItemComponent {
  @Input() lookupOption!: LookupOption;
  @Input() is_selected = false;
  @Output('update_selections') updateSelections: EventEmitter<any> = new EventEmitter();
  PlatformMediaTypeLabel = PlatformMediaTypeLabel;

  onUpdateClick() {
    this.updateSelections.emit({option:this.lookupOption,is_selected:this.is_selected});
  }
}
