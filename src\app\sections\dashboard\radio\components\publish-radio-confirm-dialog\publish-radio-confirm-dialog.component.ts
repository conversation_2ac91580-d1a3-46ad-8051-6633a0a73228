import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { RadioService } from '../../../../../services/radio-service';
import { RadioType } from '../../../../../types/radio';

@Component({
  selector: 'app-publish-radio-confirm-dialog',
  templateUrl: './publish-radio-confirm-dialog.component.html',
  styleUrl: './publish-radio-confirm-dialog.component.css'
})
export class PublishRadioConfirmDialogComponent {
  constructor(
      @Inject(MAT_DIALOG_DATA) public data: RadioType,
      private dialogRef: MatDialogRef<PublishRadioConfirmDialogComponent>,
      private readonly radioService: RadioService
    ) {}
  
    onConfirm () {
      this.radioService.patchRadioPublishState(this.data.id, true).subscribe({
        next: () => {
          this.onBack(true);
        },
        error: () => {
          
        },
      });
    }
  
    onBack(success: boolean = false) {
      this.dialogRef.close(success);
    }
  
    onCancel () {   
      this.onBack();
    }
}
