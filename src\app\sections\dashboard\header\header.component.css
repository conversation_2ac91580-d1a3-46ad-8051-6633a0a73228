@import './../../../components//multitarget-autocomplete.component.css';

:host {
    width: 100%;
    border-bottom: 1px solid #ffffff40;
    color: white;
    height: 68px;
}

.search-input {
    --mdc-outlined-text-field-input-text-color: white;
    --mat-form-field-container-text-size: 14px;
    --mat-form-field-container-text-line-height: 20px;
    --mat-form-field-container-height: 28px;
    --mat-form-field-container-text-weight: 500;
    --mat-form-field-container-vertical-padding: 4px;
    --as-border-color: transparent;
    --mdc-outlined-text-field-hover-outline-color: var(--as-border-color);
    --mdc-outlined-text-field-outline-color: var(--as-border-color);
    --mdc-outlined-text-field-focus-outline-color: var(--as-border-color);
    --mdc-outlined-text-field-input-text-placeholder-color: white;
    --mdc-outlined-text-field-caret-color: white;
    border-radius: 10px;
    background-color: #ffffff40;
    --mdc-outlined-text-field-container-shape: 12px;
    font-family: "Visby CF";
    color: white;
    width: 356px;
}

.search-input-icon {
    height: 18px;
    width: 18px;
    line-height: 18px;
    padding: 0px 4px 0px 8px !important;
}

p {
    margin: 0px;
}

.header-icon {
    margin-left: 65px;
    margin-right: 8px;
    padding: 4px;
    width: 20px;
    height: 20px;
}

.header-breadcrumb-item {
    margin-bottom: 0px;
    font-family: "Visby CF";
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
}

.header-breadcrumb-item-animation {
    transition: margin 500ms ease-in-out;
}

.header-breadcrumb-item-animation:hover {
    margin-left: 8px;
    margin-right: 8px;
    transition: margin 500ms ease-in-out;
}

.header-breadcrumb-item-highlighted {
    font-weight: 700;
}

.header-separator {
    padding: 0px 8px;
    font-weight: 400;
}

/**/
.search-display-button {
    width: 304px;
    display: flex;
    gap: 8px;
    border-radius: 8px;
    background-color: #ffffff40;
    border: none;
    color: white;
    padding: 5px 0px;
    margin-right: 28px;
}

.search-display-icon {
    height: 20px;
    width: 20px;
    font-size: 20px;
    padding: 0px 8px 0px 8px;
}

.search-display-button-text {
    font-size: 12px;
    line-height: 20px;
    font-family: "Visby CF";
    font-weight: 600;
    margin: 0px;
    flex: 1;
    text-align: start;
}