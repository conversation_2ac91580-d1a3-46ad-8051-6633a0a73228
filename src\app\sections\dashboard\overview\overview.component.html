<style>
  :host {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .main-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-left: 85px;
  }

  .overview-card {
    width: 100%;
    height: 100%;
  }

  .suscriptors-graph-card {
    display: flex;
    flex-direction: column;
  }

  .card-container {
    height: 100%;
    width: 32%;
  }

  .top-card {
    border-radius: 12px;
    background-color: #e3f5ff;
    width: 100%;
    height: 132px;
    justify-self: start;
  }

  .top-card-data {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .top-card-data ::ng-deep h4,
  h6 {
    margin: 0;
  }

  .graph-card {
    border-radius: 12px;
    background-color: #f7f9fb;
    width: 100%;
    min-height: 330px;
    margin-top: 14px;
    margin-bottom: 14px;
  }

  .graph-card-header {
    font-family: "Visby CF";
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0em;
    text-align: left;
    --mdc-text-button-label-text-color: #201f28;
    --mdc-text-button-label-text-weight:400;
  }

  .graph-card-header-minor {
    font-family: "Visby CF";
    font-size: 12px;
    font-weight: 500;
    line-height: 18px;
    margin-bottom: 0px;
    letter-spacing: 0em;
    margin-left: 0.5rem;
    margin-right: 10px;
    text-align: left;
    color: #201f28;
  }

  .resume-card {
    border-radius: 12px;
    background-color: #f7f9fb;
    width: 49%;
    min-height: 232px;
    height: fit-content;
    display: flex;
    flex-direction: column;
  }

  .resume-card-header {
    font-family: "Visby Semibold";
    font-size: 14px;
    font-weight: 700;
    line-height: 20px;
    letter-spacing: 0em;
    text-align: left;
  }

  .overview-header {
    font-family: "Visby CF";
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    letter-spacing: 0em;
    text-align: left;
    color: white;
    margin: 0px;
  }

  .overview-suscriptors {
    width: 100%;
    height: 132px;
    display: flex;
    flex-direction: column;
  }

  .chart-container {
    width: 100%;
    flex: 1;
    min-height: 200px;
    display: flex;
    justify-content: center;
  }

  .overview-charts-container {
    width: 82.5%;
    margin-top: 36px;
  }

  .utilities-container {
    width: 82.5%;
    margin-top: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .suscriptions-line-chart {
    margin-top: 10px;
    width: 100% !important;
  }

  .overview-bottom-container {
    height: 280px;
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .location-pie-chart {
    height: 200px !important;
    max-width: 500px;
  }

  .device-bar-chart {
    height: 200px !important;
  }

  .dot {
    height: 6px;
    width: 6px;
    background-color: black;
    border-radius: 50%;
    display: inline-block;
  }

  .as-divider {
    height: 20px; 
    margin: 0px 20px 0px 12px;
    --mat-divider-color:#1C1C1C33;
  }

  .suscriptors-header {
    font-family: "Visby CF";
    font-size: 12px;
    font-weight: 700;
    line-height: 18px;
    text-align: left;
    color: #46535B;
  } 

  .suscriptors-total {
    font-family: "Inter";
    font-size: 24px;
    font-weight: 600;
    line-height: 36px;
    text-align: left;
    color: #1C1C1C;
  }

  .suscriptors-tendency {
    font-family: "Visby CF";
    font-size: 12px;
    font-weight: 500;
    line-height: 18px;
    text-align: left;
    color: #1C1C1C;
  }

  .suscriptors-tendency-icon {
    width: 14px;
    height: 14px;
    font-size: 14px;
    margin-left: 8px;
  }

  .tendency-rotation {
    transform: rotate(180deg);
  }

  @media screen and (max-width: 1344px) {
    .utilities-container {
      min-width: 907px;
    }
    .overview-charts-container {
      min-width: 907px;
    }
  } 

  @media screen and (min-width: 1440px) {
    .utilities-container {
      width: 78%;
    }

    .overview-charts-container {
      width: 78%;
    }

    .card-container {
      width: 32%;
    }
  }

  @media screen and (min-height: 1020px) {
    .overview-container {
      height: 100%;
    }

    .graph-card {
      margin-top: 28px;
      margin-bottom: 28px;
    }

    .overview-charts-container {
      margin-top: 32px;
    }

    .overview-bottom-container {
      height: 35.1%;
    }

    .overview-utilities-container {
      margin-top: 28px;
    }

    .card-container {
      height: fit-content;
    }
  }
</style>
<div class="main-container">
  <form class="utilities-container" [formGroup]="overviewSearchForm">
    <p class="overview-header">Overview</p>
    <div style="display: flex; align-self: flex-end; gap: 17px;">
      <div style="display: flex; flex-direction: column">
        <p class="custom-date-picker-header">Desde</p>
        <mat-form-field floatLabel="always" subscriptSizing="dynamic" appearance="outline"
          class="custom-date-picker expand-datepicker">
          <input matInput [matDatepicker]="datepicker" [formControl]="overviewSearchForm.controls.initDate" readonly
            [min]="getMinSearchDate()"
            [max]="overviewSearchForm.controls.limitDate.value !== null?overviewSearchForm.controls.limitDate.value.clone().subtract(1,'d'):maxDate.clone().subtract(1,'d')"
            [disabled]="true" class="custom-date-picker-input" />
          <mat-icon matPrefix class="custom-date-picker-icon">event</mat-icon>
          <mat-datepicker-toggle matIconSuffix [for]="datepicker" class="custom-date-picker-toggle-icon">
            <mat-icon matDatepickerToggleIcon class="custom-date-picker-toggle-icon">
              keyboard_arrow_down
            </mat-icon>
          </mat-datepicker-toggle>
          <mat-datepicker panelClass="menu-backdrop" #datepicker [calendarHeaderComponent]="customHeader">
            <mat-datepicker-actions>
              <button mat-button matDatepickerCancel style="border-radius: 12px">
                Cancelar
              </button>
              <button mat-raised-button matDatepickerApply class="custom-date-picker-apply-btn">
                Aceptar
              </button>
            </mat-datepicker-actions>
          </mat-datepicker>
        </mat-form-field>
      </div>
      <div style="display: flex; flex-direction: column">
        <p class="custom-date-picker-header">Hasta</p>
        <mat-form-field floatLabel="always" subscriptSizing="dynamic" appearance="outline"
          class="custom-date-picker expand-margin-datepicker">
          <input matInput [matDatepicker]="datepickerv" [formControl]="overviewSearchForm.controls.limitDate" readonly
            [min]="asMinDate" [max]="maxDate" placeholder=" -- / -- / -- "
            class="custom-date-picker-input expand-datepicker-input"
            [style]="overviewSearchForm.controls.limitDate.value === null ? 'letter-spacing: 2.5px;':''" />
          <mat-icon matPrefix class="custom-date-picker-icon">event</mat-icon>
          <mat-datepicker-toggle matIconSuffix [for]="datepickerv" class="custom-date-picker-toggle-icon">
            <mat-icon matDatepickerToggleIcon class="custom-date-picker-toggle-icon">keyboard_arrow_down
            </mat-icon>
          </mat-datepicker-toggle>
          <mat-datepicker panelClass="menu-backdrop" #datepickerv disabled="false"
            [calendarHeaderComponent]="customHeader">
            <mat-datepicker-actions>
              <button mat-button matDatepickerCancel>Cancelar</button>
              <button mat-raised-button color="primary" matDatepickerApply class="custom-date-picker-apply-btn">
                Aceptar
              </button>
            </mat-datepicker-actions>
          </mat-datepicker>
        </mat-form-field>
      </div>
    </div>
  </form>
  <div class="overview-charts-container">
    <div class="overview-suscriptors">
      <!---->
      <ng-template #elseBlockSuscriptors>
        <div class="top-card-data">
          <!--<h4>????</h4>-->
        </div>
      </ng-template>
      <!---->
      <ng-template #thenBlockSuscriptors let-data="data">
        <div class="top-card-data">
          <h4 class="suscriptors-total">
            {{ qtyFormat(data.limit_qty) }}
          </h4>
          <div style="display: flex; align-items: center">
            <h6 class="suscriptors-tendency">
              {{
                getAmmountVariation(data.start_qty,data.limit_qty).toFixed(2) + "%"
              }}
            </h6>
            <mat-icon svgIcon="black_tendency_icon" class="suscriptors-tendency-icon" [class]="getAmmountVariation(data.start_qty,data.limit_qty) > 0 ? '':'tendency-rotation'"></mat-icon>
          </div>
        </div>
      </ng-template>
      <!---->
      <div style="display: flex;height: 100%;justify-content: space-between;">
          <div class="card-container">
            <mat-card class="top-card">
              <mat-card-content>
                <div tyle="height: 24px !important;">
                  <mat-icon svgIcon="movistar_icon"></mat-icon>
                </div>
                <h5 class="suscriptors-header">Suscriptores Movistar</h5>
                <ng-container
                  [ngTemplateOutlet]="
                    loadingSuscriptorsData === false
                      ? thenBlockSuscriptors
                      : elseBlockSuscriptors
                  "
                  [ngTemplateOutletContext]="{
                    data: loadingSuscriptorsData === false?suscriptorsData['MOVISTAR']:null
                  }"
                >
                </ng-container>
              </mat-card-content>
            </mat-card>
          </div>
          <div class="card-container">
            <mat-card class="top-card">
              <mat-card-content>
                <div style="height: 29px !important">
                  <mat-icon svgIcon="digitel_icon"></mat-icon>
                </div>
                <h5 class="suscriptors-header">Suscriptores Digitel</h5>
                <ng-container
                  [ngTemplateOutlet]="
                    loadingSuscriptorsData === false
                      ? thenBlockSuscriptors
                      : elseBlockSuscriptors
                  "
                  [ngTemplateOutletContext]="{
                    data: loadingSuscriptorsData === false?suscriptorsData['DIGITEL']:null
                  }"
                >
                </ng-container>
              </mat-card-content>
            </mat-card>
          </div>
          <div class="card-container">
            <mat-card class="top-card">
              <mat-card-content>
                <div tyle="height: 24px !important;">
                  <mat-icon svgIcon="group_icon"></mat-icon>
                </div>
                <h5 class="suscriptors-header">Total Suscriptores</h5>
                <div
                  class="top-card-data"
                  *ngIf="
                    loadingSuscriptorsData === false;
                    else elseBlockSuscriptors
                  "
                >
                  <h4 class="suscriptors-total">
                    {{qtyFormat(getTotalSuscriptors())}}
                  </h4>
                  <div style="display: flex; align-items: center">
                    <h6 class="suscriptors-tendency">
                      {{getTotalSuscriptorsTendency().toFixed(2) + "%"}}
                    </h6>
                    <mat-icon svgIcon="black_tendency_icon" class="suscriptors-tendency-icon" [class]="getTotalSuscriptorsTendency() > 0 ? '':'tendency-rotation'"></mat-icon>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
      </div>
    </div>
    <mat-card class="graph-card">
      <mat-card-content class="suscriptors-graph-card">
        <div style="display: flex; align-items: center">
          <div style="display: flex;align-items: center;gap: 10px;">
            @for (option of carrierOptions; track option) {
              <button mat-button disableRipple="true" class="graph-card-header" 
              (click)="setCarrierOption(option.value)"
              [style]="overviewSearchForm.controls.carrierOption.value === option.value?'font-weight:600':''">{{option.label}}</button>
            }
          </div>
          <mat-divider
            [vertical]="true"
            class="as-divider"
          ></mat-divider>
          <div style="display: flex; align-items: center">
            <span class="dot" style="background-color: #550dc5"></span>
            <h5 class="graph-card-header-minor">Suscripciones</h5>
          </div>
          <div style="display: flex; align-items: center">
            <span class="dot" style="background-color: #1be2ed"></span>
            <h5 class="graph-card-header-minor">Reproducciones</h5>
          </div>
        </div>
        <div class="chart-container">
          <canvas id="suscriptorsChart" class="suscriptions-line-chart" [style]="loadingSuscriptorsData  === true? 'display:none':''">{{
            suscriptorsChart
          }}</canvas>
        </div>
      </mat-card-content>
    </mat-card>
    <div class="overview-bottom-container">
      <mat-card class="resume-card">
        <mat-card-content>
          <p class="resume-card-header">Tráfico por dispositivo</p>
          <div class="chart-container" style="justify-content: start;">
            <canvas id="deviceTrafficChart" class="device-bar-chart" [style]="loadingSuscriptorsData  === true? 'display:none':''">{{
              deviceTrafficChart
            }}</canvas>
          </div>
        </mat-card-content>
      </mat-card>
      <mat-card class="resume-card">
        <mat-card-content>
          <p class="resume-card-header">Tráfico por ubicación</p>
          <div class="chart-container">
            <canvas id="locationTrafficChart" class="location-pie-chart" [style]="loadingSuscriptorsData  === true? 'display:none':''">{{
              locationTrafficChart
            }}</canvas>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>