import { AfterViewInit, Component, OnInit, ViewChild } from "@angular/core";
import { CustomSnackbarV2Component } from "../../../../../components/custom-snackbar-v2/custom-snackbar-v2.component";
import { MatSnackBar } from "@angular/material/snack-bar";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { AbstractControl } from "@angular/forms";
import { MatTabChangeEvent, MatTabGroup } from "@angular/material/tabs";
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  Observable,
  of,
  Subject,
  switchMap,
  takeUntil,
  tap,
} from "rxjs";
import { SearchService } from "../../../../../services/search-service";
import { GenreService } from "../../../../../services/genre-service";
import { ArtistService } from "../../../../../services/artist-service";
import { SongType } from "../../../../../types/song";
import { GenreType } from "../../../../../types/genre";
import { ArtistType } from "../../../../../types/artist";
import {
  ContentConfig,
  CreateRadioData,
  LookupOption,
  LookupType,
} from "../../../../../types/radio";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { RadioFinishProcessDialogComponent } from "../radio-finish-process-dialog/radio-finish-process-dialog.component";
import { RadioService } from "../../../../../services/radio-service";
import { PlatformMediaService } from "../../../../../services/platform-media-service";
import { PlatformMedia, PlatformMediaType, PlatformMediaTypeLabel } from "../../../../../types/platform-media";
import { RoutingService } from "../../../../../services/routing-service";
import { ProcessFeedbackDialogComponent } from "../../../../../components/process-feedback-dialog/process-feedback-dialog.component";
import { CustomSnackbarComponent } from "../../../../../components/custom-snackbar/custom-snackbar.component";

export function mimeTypeValidator(
  control: AbstractControl
): { [key: string]: any } | null {
  const file = control.value as File;
  if (!file) {
    return null;
  }
  const allowedMimeTypes = ["image/png", "image/jpeg", "image/jpg"];
  const isValid = allowedMimeTypes.includes(file.type);
  return isValid ? null : { invalidMimeType: true };
}

function nonEmptyArrayObjectValidator(
  control: AbstractControl
): { [key: string]: any } | null {
  const isValid = Object.entries(control.value as ContentConfig).find(([,e]) => e.length > 0);
  return isValid ? null : { invalidArrayObject: true };
}

function nonEmptyArrayAdsObjectValidator(
  control: AbstractControl
): { [key: string]: any } | null {
  const isValid = Object.entries(control.value as {id:number}[]).length > 0;
  return isValid ? null : { invalidArrayObject: true };
}

function lengthArrayObjectValidator(
  control: AbstractControl
): { [key: string]: any } | null {
  const isValid = Object.entries(control.value as {id:number}[]).length <= 10;
  return isValid ? null : { invalidArrayObject: true };
}

@Component({
  selector: "app-radio-config-tabs-form",
  templateUrl: "./radio-config-tabs-form.component.html",
  styleUrl: "./radio-config-tabs-form.component.css",
})
export class RadioConfigTabsFormComponent implements OnInit, AfterViewInit {
  @ViewChild("radioFormTabs") radioFormTabs!: MatTabGroup;
  selectedTabIndex: number = 0; 
  asRadioCover: string | null = null;
  isRadioCoverFieldHovered: boolean = false;
  processFinishedDialogRef: MatDialogRef<RadioFinishProcessDialogComponent> | undefined = undefined;
  destroy$: Subject<boolean> = new Subject<boolean>();
  isLoading: boolean = false;
  isSaving: boolean = false;
  lookupTypeEnum = LookupType;
  welcomePlatformContent: number[] = [];
  platformContent: number[] = [];


  constructor(
    private _snackBar: MatSnackBar,
    private _dialog: MatDialog,
    private readonly searchService: SearchService,
    private readonly genreService: GenreService,
    private readonly artistService: ArtistService,
    private readonly radioService: RadioService,
    private readonly platformMediaService: PlatformMediaService,
    private readonly routingService: RoutingService,
  ) {}

  lookupTypes = [
    { label: "Canciones", value: LookupType.SONG },
    { label: "Géneros", value: LookupType.GENRE },
    { label: "Artistas", value: LookupType.ARTIST },
  ];

  activeLookupType = this.lookupTypes[0].value;

  radioForm = new FormGroup({
    title: new FormControl<string>("", [
      Validators.required,
      Validators.minLength(1),
      Validators.maxLength(22),
    ]),
    author: new FormControl<string>("", [
      Validators.required,
      Validators.minLength(1),
      Validators.maxLength(22),
    ]),
    coverImageUrl: new FormControl<File | null>(null, [
      Validators.required,
      mimeTypeValidator
    ]),
    is_public: new FormControl<boolean>(false),
  });

  searchGroup = new FormGroup({
    searchControl: new FormControl<string>(""),
    lookupType: new FormControl<number>(this.lookupTypes[0].value),
    contentConfig: new FormControl<ContentConfig>({
      artistIds: [],
      genreIds: [],
      albumIds: [],
      songIds: [],
    },[nonEmptyArrayObjectValidator]),
  });

  adsSearch = new FormGroup({
    adsControl: new FormControl<string>(""),
    adsSelection: new FormControl<{id:number}[]>([],
      []),
  });

  contentSearch = new FormGroup({
    contentControl: new FormControl<string>(""),
    contentSelection: new FormControl<{id:number}[]>([],
      [lengthArrayObjectValidator]),
  });
  
  searchOptions: LookupOption[] | null = null;
  searchOptionsPlatformMedia: LookupOption[] | null = null;
  searchOptionsContentMedia: any = null;

  lookupToContentConfigPropertyMap: Record<number,keyof ContentConfig> = {
    [LookupType.SONG]: "songIds",
    [LookupType.GENRE]: "genreIds",
    [LookupType.ARTIST]: "artistIds",
  }

  lookupTypeConfigMapSummary: Record<number, LookupOption[]> = {
    [LookupType.SONG]: [],
    [LookupType.GENRE]: [],
    [LookupType.ARTIST]: [],
  };

  lookupTypeConfigMap: Record<number, number[]> = {
    [LookupType.SONG]: [],
    [LookupType.GENRE]: [],
    [LookupType.ARTIST]: [],
  };

  lookupTypeMapPlatformMedia: number[] = [];
  lookupTypeMapContentMedia: number[] = [];

  mouseEnter() {
    this.isRadioCoverFieldHovered = true;
  }

  mouseLeave() {
    this.isRadioCoverFieldHovered = false;
  }

  selectTab(index: number) {
    this.selectedTabIndex = index;
    this.radioFormTabs.selectedIndex = this.selectedTabIndex;
  }

  onImagePicked(event: Event) {
    const _target = event.target as HTMLInputElement;
    const file = _target.files ? _target.files[0] : null;
    if (file !== null) {
      let reader = new FileReader();
      reader.onload = (event: any) => {
        this.asRadioCover = event.target.result;
        this.radioForm.patchValue({ coverImageUrl: file });
      };
      reader.readAsDataURL(file);
    }
  }

  emitSnackbarMessage(asMessage: string = "Guardada la información con éxito") {
    this._snackBar.openFromComponent(CustomSnackbarV2Component, {
      duration: 10 * 1000,
      horizontalPosition: "center",
      verticalPosition: "bottom",
      data: {
        message: asMessage,
        asIcon: "playlist_add_check",
      },
      panelClass:["process-snackbar"],
    });
  }

  validatePlatformContent() {
    if ( !this.lookupTypeMapContentMedia.some((id) => this.platformContent.includes(id)) ){
      this._snackBar.openFromComponent(CustomSnackbarComponent, {
        duration: 5 * 1000,
        horizontalPosition: "center",
        verticalPosition: "top",
        data: { message: "Tienes que seleccionar al menos un micro de contenido" },
        panelClass: ["error-snackbar"] ,
      });
      return false;
    }
    return true;
  }

  validateWelcomePlatformContent() {
    if ( !this.lookupTypeMapContentMedia.some((id) => this.welcomePlatformContent.includes(id)) ){
      this._snackBar.openFromComponent(CustomSnackbarComponent, {
        duration: 5 * 1000,
        horizontalPosition: "center",
        verticalPosition: "top",
        data: { message: "Tienes que seleccionar al menos un micro de bienvenida" },
        panelClass: ["error-snackbar"] ,
      });
      return false;
    }
    return true;
  }

  stageSubmit(activeIndex: number) {
    if ( activeIndex === 3 ) {
      if ( !this.validateWelcomePlatformContent() || !this.validatePlatformContent() ){
        return;
      }
    }
    switch (activeIndex) {
      case 0:
        this.onSubmit();
        break;
      case 1:
        this.onSubmitRadioConfig();
        break;
      case 2:
        this.onSubmitPlatformContent(activeIndex+1);
        break;
      case 3:
        this.onSubmitPlatformContent(activeIndex+1);
        break;
      case 4:
        this.isSaving = true;
        this.onSubmitPreview(true).subscribe((res) => {
          this.openProcessFeedbackDialog("Publicación exitosa","check_circle", true);
          this.routingService.add('/dashboard/radio');
        }, (err) => {
          this.openProcessFeedbackDialog("Publicación fallida","warning_amber", false);
          this.isSaving = false;
        });
        break;
      default:
        break;
    }
  }

  onProcessStop () {
    this.processFinishedDialogRef = this._dialog.open(RadioFinishProcessDialogComponent, {
      disableClose: true,
      data: {
        ...this.radioForm.value,
        contentConfig:this.searchGroup.controls.contentConfig.value,
        platformMediaContent: [...this.lookupTypeMapPlatformMedia,...this.lookupTypeMapContentMedia],
        VALID_FORM: this.radioForm.valid,
      },
      panelClass: 'radio-process-dialog-container',
    });
    this.processFinishedDialogRef.afterClosed().subscribe(() => {
      this.radioService.setRollback(false);
      this.processFinishedDialogRef = undefined;
    });
  }

  onSubmit() {
    this.selectTab(1);
    this.emitSnackbarMessage();
  }

  onSubmitRadioConfig() {
    this.selectTab(2);
    this.emitSnackbarMessage("Guardadas las canciones con éxito");
  }

  onSubmitPlatformContent(subIndex: number) {
    this.selectTab(subIndex);
    this.emitSnackbarMessage(subIndex === 2 ? "Guardadas los anunciantes con éxito" : "Guardadas los micros con éxito");
  }

  onSubmitPreview(shouldPublish: boolean = false) {
    const {title, author, coverImageUrl} =  this.radioForm.value;
    const {contentConfig} = this.searchGroup.value;
    return this.invokeCreateRadio({
        title: title as string,
        author: author as string,
        coverImageUrl: coverImageUrl as File,
        contentConfig: {
          ...contentConfig,
        },
        platformMediaContent: [...this.lookupTypeMapPlatformMedia,...this.lookupTypeMapContentMedia],
    }, shouldPublish);
  }

  invokeCreateRadio(data:CreateRadioData, shouldPublish:boolean) {
    return this.radioService.createRadio({...data, is_public: shouldPublish });
  }

  onClearSearch() {
    this.searchGroup.controls.searchControl.reset();
  }

  onAdsClearSearch() {
    this.adsSearch.controls.adsControl.reset("");
  }

  onContentClearSearch() {
    this.contentSearch.controls.contentControl.reset("");
  }

  onSearchSongs(search: string) {
    return this.searchService.searchByNameV2(search);
  }

  onSearchGenre(search: string) {
    return this.genreService.getGenresByMatch(search);
  }

  onSearchArtist(search: string) {
    return this.artistService.getArtistByMatch(search);
  }

  lookupOptionMap(
    lookupData: SongType[] | GenreType[] | ArtistType[],
    lookupType: LookupType
  ) {
    switch (lookupType) {
      case LookupType.SONG:
        return (lookupData as SongType[]).map((song) => ({
          name: song.name,
          optionType: lookupType,
          optionId: song.songid,
          cover: song.cover,
          label: song.artist,
        }));
      case LookupType.GENRE:
        return (lookupData as GenreType[]).map((genre) => ({
          name: genre.nombre,
          optionType: lookupType,
          optionId: genre.id,
          cover: "./assets/lookup_placeholder.png",
          label: "Aqustico",
        }));
      case LookupType.ARTIST:
        return (lookupData as ArtistType[]).map((artist) => ({
          name: artist.nombre,
          optionType: lookupType,
          optionId: artist.id,
          cover: "./assets/lookup_placeholder.png",
          label: "Artista",
        }));
      default:
        return [];
    }
  }

  lookupSwitch(
    search: string,
    lookupType: LookupType
  ): Observable<LookupOption[]> {
    return of({ search, lookupType }).pipe(
      filter(
        ({ search }) => typeof search === "string" && search.trim() !== ""
      ),
      switchMap(({ search, lookupType }) => {
        switch (lookupType) {
          case LookupType.SONG:
            return this.onSearchSongs(search).pipe(
              map((res) =>
                this.lookupOptionMap(res.body as SongType[], LookupType.SONG)
              )
            );
          case LookupType.GENRE:
            return this.onSearchGenre(search).pipe(
              map((res) =>
                this.lookupOptionMap(res.body as GenreType[], LookupType.GENRE)
              )
            );
          case LookupType.ARTIST:
            return this.onSearchArtist(search).pipe(
              map((res) =>
                this.lookupOptionMap(res.body as ArtistType[], LookupType.ARTIST)
              )
            );
          default:
            return of([] as LookupOption[]);
        }
      })
    );
  }

  updateSelections(event: { option: LookupOption; is_selected: boolean }) {
    if (event.is_selected) {
      this.removeFromConfig(event.option);
    } else {
      this.addToConfig(event.option);
    }
    this.searchGroup.patchValue({
      contentConfig: Object.fromEntries(
        Object.entries(this.lookupTypeConfigMap)
        .map(([k,v]) => [this.lookupToContentConfigPropertyMap[parseInt(k) as LookupType],v])
      )
    })
  }

  updateAdsSelections(event: { option: LookupOption; is_selected: boolean }) {
    if(event.is_selected){
      this.lookupTypeMapPlatformMedia = this.lookupTypeMapPlatformMedia.filter((id) => id !== event.option.optionId);
    }
    else {
      this.lookupTypeMapPlatformMedia.push(event.option.optionId);
    }
    this.adsSearch.patchValue({
      adsSelection: this.lookupTypeMapPlatformMedia.map((id) => ({id}))
    })
  }

  updatePlatformContentSelections(event: { option: LookupOption; is_selected: boolean }) {
    if(event.is_selected){
      this.lookupTypeMapContentMedia = this.lookupTypeMapContentMedia.filter((id) => id !== event.option.optionId);
    }
    else {
      this.lookupTypeMapContentMedia.push(event.option.optionId);
    }
    this.contentSearch.patchValue({
      contentSelection: this.lookupTypeMapContentMedia.map((id) => ({id}))
    });
  }

  addToConfig(option: LookupOption) {
    this.lookupTypeConfigMapSummary[option.optionType].push(option);
    this.lookupTypeConfigMap[option.optionType].push(option.optionId);
  }

  removeFromConfig(option: LookupOption) {
    this.lookupTypeConfigMapSummary[option.optionType] = this.lookupTypeConfigMapSummary[
      option.optionType
    ].filter((op) => op.optionId !== option.optionId);
    this.lookupTypeConfigMap[option.optionType] = this.lookupTypeConfigMap[
      option.optionType
    ].filter((id) => id !== option.optionId);
  }

  onBackAction (activeIndex:number) {
    if(activeIndex !== 4) {
      this.selectTab(activeIndex - 1);
    }
    else if (activeIndex === 4) {
      this.isSaving = true;
      this.onSubmitPreview().subscribe({  next :(res) => {
        this.openProcessFeedbackDialog("Guardado exitoso","check_circle", true);
        this.routingService.add('/dashboard/radio')
        },
        error: (err) => {
          this.isSaving = false;
        }
      });
    }
  }

  openProcessFeedbackDialog(message:string, icon:string, isPositive:boolean) {
    this._dialog.open(ProcessFeedbackDialogComponent,{
      data: {
        message,
        icon,
        isPositive
      },
      panelClass: "process-feedback-dialog-container",
    });
  }

  ngOnInit(): void {
    this.radioService.isRollbackActive$.pipe(takeUntil(this.destroy$)).subscribe((isActive: boolean) => {
      if(isActive) {
        this.onProcessStop();
      }
    });
  }

  ngAfterViewInit(): void {
    this.radioFormTabs.focusChange.subscribe((ev: MatTabChangeEvent) => {
      this.selectedTabIndex = ev.index;
    });
    this.radioFormTabs.selectedIndexChange.pipe(
      filter((ind) => ind === 2),
      tap(() => {
        this.isLoading = true;
      }),
      switchMap(() => this.platformMediaService.getPlatformMediaByType(PlatformMediaType.ADS)),
      map((res) => (res.body as PlatformMedia[]).map((item) => ({
        name: item.name,
        optionType: LookupType.PLATFORM_MEDIA,
        optionId: item.id,
        cover: item.propietaryBrandIcon ? item.propietaryBrandIcon : "./assets/lookup_placeholder.png",
        label: item.propietaryBrand ?  item.propietaryBrand :'Aqustico',
      })))
    ).subscribe((results) => {
      this.searchOptionsPlatformMedia = results.length > 0 ? results : null;
      this.isLoading = false;
    });
    this.radioFormTabs.selectedIndexChange.pipe(
      filter((ind) => ind === 3),
      tap(() => {
        this.isLoading = true;
      }),
      switchMap(() => this.platformMediaService.getPlatformMediaByType([
        PlatformMediaType.PLATFORM_CONTENT, 
        PlatformMediaType.WELCOME_PLATFORM_CONTENT
      ])),
      tap((res) => {
        this.welcomePlatformContent = res.body!.filter((item: any) => 
          item.mediaType === PlatformMediaTypeLabel.WELCOME_PLATFORM_CONTENT
        ).map((item: any) => item.id);
        this.platformContent = res.body!.filter((item: any) => 
          item.mediaType === PlatformMediaTypeLabel.PLATFORM_CONTENT
        ).map((item: any) => item.id);
      }),
      map((res) => (res.body as PlatformMedia[]).map((item) => ({
        name: item.name,
        optionType: LookupType.PLATFORM_MEDIA,
        optionId: item.id,
        cover: item.propietaryBrandIcon ? item.propietaryBrandIcon : "./assets/lookup_placeholder.png",
        label: item.propietaryBrand ?  item.propietaryBrand :'Aqustico',
        mediaTypeLabel: item.mediaType,
      })))
    ).subscribe((results) => {
      this.searchOptionsContentMedia = results.length > 0 ? results : null;
      this.isLoading = false;
    });

    this.searchGroup.controls.searchControl.valueChanges
      .pipe(
        distinctUntilChanged(),
        filter((v) => typeof v === "string" && v.trim() !== ""),
        debounceTime(500),
        tap(() => {
          this.isLoading = true;
        })
      )
      .subscribe((value) => {
        this.lookupSwitch(
          value as string,
          this.searchGroup.controls.lookupType.value as number
        ).subscribe((results) => {
          this.searchOptions = results.length > 0 ? results : null;
          this.isLoading = false;
        });
      });

    this.searchGroup.controls.lookupType.valueChanges
      .pipe(
        tap(() => {
          this.isLoading = true;
        })
      )
      .subscribe((value) => {
        this.lookupSwitch(
          this.searchGroup.controls.searchControl.value as string,
          value as number
        ).subscribe((results) => {
          this.searchOptions = results.length > 0 ? results : null;
          this.isLoading = false;
        });
      });

      this.adsSearch.controls.adsControl.valueChanges.pipe(
        distinctUntilChanged(),
        filter((v) => typeof v === "string"),
        debounceTime(500),
        map((value) => (value as string).trim()), 
        tap(() => {
          this.isLoading = true;
        }),
        switchMap((value) => {
          if(value === "") {
            return this.platformMediaService.getPlatformMediaByType(PlatformMediaType.ADS);
          }
          return this.platformMediaService.getPlatformMedia(value as string,PlatformMediaType.ADS);
        }),
        map((res) => {
          return (res.body as PlatformMedia[]).map((item) => ({
            name: item.name,
            optionType: LookupType.PLATFORM_MEDIA,
            optionId: item.id,
            cover: item.propietaryBrandIcon ? item.propietaryBrandIcon : "./assets/lookup_placeholder.png",
            label: item.propietaryBrand ?  item.propietaryBrand :'Aqustico',
          }));
        })
      )
      .subscribe((results) => {
        this.searchOptionsPlatformMedia = results.length > 0 ? results : null;
        this.isLoading = false;
      });

      this.contentSearch.controls.contentControl.valueChanges.pipe(
        distinctUntilChanged(),
        filter((v) => typeof v === "string"),
        debounceTime(500),
        map((value) => (value as string).trim()), 
        tap(() => {
          this.isLoading = true;
        }),
        switchMap((value) => {
          if(value === "") {
            return this.platformMediaService.getPlatformMediaByType(PlatformMediaType.PLATFORM_CONTENT)
          }
          return this.platformMediaService.getPlatformMedia(value as string);
        }),
        map((res) => {
          return (res.body as PlatformMedia[]).map((item) => ({
            name: item.name,
            optionType: LookupType.PLATFORM_MEDIA,
            optionId: item.id,
            cover: item.propietaryBrandIcon ? item.propietaryBrandIcon : "./assets/lookup_placeholder.png",
            label: item.propietaryBrand ?  item.propietaryBrand :'Aqustico',
          }));
        })
      )
      .subscribe((results) => {
        this.searchOptionsContentMedia = results.length > 0 ? results : null;
        this.isLoading = false;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
  }
}
