import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ProcessFeedbackDialogComponent } from './process-feedback-dialog.component';

describe('ProcessFeedbackDialogComponent', () => {
  let component: ProcessFeedbackDialogComponent;
  let fixture: ComponentFixture<ProcessFeedbackDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ProcessFeedbackDialogComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(ProcessFeedbackDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
