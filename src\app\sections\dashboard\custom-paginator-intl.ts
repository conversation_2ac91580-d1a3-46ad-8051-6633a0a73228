import {Injectable} from '@angular/core';
import {MatPaginatorIntl} from '@angular/material/paginator';
import {Subject} from 'rxjs';

@Injectable()
export class CustomPaginatorIntl implements MatPaginatorIntl {
  changes = new Subject<void>();
  firstPageLabel = `Primera página`;
  itemsPerPageLabel = `Filas por página:`;
  lastPageLabel = `Última página`;
  nextPageLabel = 'Página Siguiente';
  previousPageLabel = 'Página Anterior';
  getRangeLabel(page: number, pageSize: number, length: number): string {
    if (length === 0) {
      return `1 de 1`;
    }
    const amountPages = Math.ceil(length / pageSize);
    return `${page + 1} de ${amountPages}`;
  }
}