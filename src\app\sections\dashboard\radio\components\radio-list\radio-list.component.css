
:host {
    flex: 1;
    display: flex;
    padding: 56px 0px 24px 81px;
    box-sizing: border-box;
    flex-direction: column;
    gap: 40px;
    overflow-y: scroll;
}

.section-header,
.section-content {
    display: flex;
    max-width: 959px;
}

.section-header {
    width: 100%;
    align-items: center;
    height: fit-content;
    justify-content: space-between;

    p {
        font-family: 'Visby CF';
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        color: #fff;
        line-height: 20px;
        margin: 0px;
    }
}

.section-content {
    width: 100%;
    flex-direction: column;
}

.radio-list-table {
    --mat-table-header-headline-color: #550DC5;
    --mat-table-row-item-label-text-color: #201F28;
    --mat-table-row-item-label-text-font: "Visby CF";
    --mat-table-row-item-label-text-size: 14px;
    --mat-table-row-item-label-text-weight: 500;
    --mat-table-row-item-container-height:64px;
    line-height: 20px;

    ::ng-deep thead {
        --mat-table-row-item-outline-color: transparent;
    }

    .mdc-data-table__cell, .mdc-data-table__header-cell {
        padding: 0px 24px;
    }

    .mat-mdc-row:hover {
        background-color: rgba(233, 233, 233, 0.95);  
    }

    --mat-table-row-item-outline-color: rgba(168, 197, 218, 0.60);
    --mat-table-background-color: #F7F9FB;
    border-radius: 12px 12px 0px 0px;
    overflow: hidden;
}

.radio-list-paginator {
    --mat-paginator-container-background-color:#F7F9FB;
    border-top: 1px solid rgba(168, 197, 218, 0.60);
    border-radius: 0px 0px 12px 12px;
}

.actions-header {
    text-align: end;
}

.actions-column-row {
    display: flex;
    justify-content: end;
    height: var(--mat-table-row-item-container-height);
    align-items: center;
    gap: 16px;
}

.common-flat-button {
    --mdc-filled-button-label-text-font:"Visby SemiBold CF";
    --mdc-filled-button-label-text-size:16px;
    --mdc-filled-button-label-text-weight:700;
    --mdc-filled-button-container-height:40px;
    --mdc-filled-button-container-shape:12px;
    --mat-filled-button-horizontal-padding: 32px;
    --mdc-filled-button-label-text-color:#1A191F;
    --mdc-filled-button-container-color:#1BE2ED;
    --mdc-filled-button-label-text-tracking:1px;
}

.on-hover-flat-button {
    transition: color 0.5s ease, background-color 0.5s ease;
    &:hover {
        --mdc-filled-button-label-text-color:#ffffff;
        --mdc-filled-button-container-color:#FFA01B;
    }
}

.action-button-icon-ext {
    &.mat-mdc-fab.mat-accent {
        --mdc-fab-container-color: #1BE2ED;
        --mat-fab-foreground-color: #201F28;
        &.unfinished-radio {
            --mdc-fab-container-color: #FFD53D;
        }
    }

    --mdc-extended-fab-container-height:32px;
    --mdc-extended-fab-label-text-font:"Visby SemiBold CF";
    --mdc-extended-fab-label-text-weight:700;
    --mdc-extended-fab-container-shape:8px;
    --mdc-extended-fab-container-elevation-shadow:none;
    --mdc-extended-fab-hover-container-elevation-shadow:none;
    --mdc-extended-fab-focus-container-elevation-shadow:none;
    --mdc-extended-fab-pressed-container-elevation-shadow:none;
    --mdc-extended-fab-label-text-tracking: 1px;
    height: var(--mdc-extended-fab-container-height);
    --mat-fab-state-layer-color: #fff;
    --mat-fab-ripple-color: rgba(255, 255, 255, 0.1);

    ::ng-deep .mat-mdc-button-touch-target {
        height:  var(--mdc-extended-fab-container-height);;
    }
}

.action-button-icon {
    --mdc-icon-button-state-layer-size:32px;
    background-color: #1BE2ED;
    border-radius: 8px;
    padding: 4px;

    ::ng-deep .mat-mdc-button-touch-target {
        height:  var(--mdc-icon-button-state-layer-size);
        width: var(--mdc-icon-button-state-layer-size);
    }
}

.on-hover-overlay {
    &::before {
        content: "";
        border-radius:inherit;
        position: absolute;
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.20) 0%, rgba(0, 0, 0, 0.20) 100%);
        opacity: 0;
        transition: opacity 0.5s ease;
    }

    &:hover {
        &::before {
            opacity: 1;
        }
    }
}

.on-hover-overlay-icon-button {
    --mat-icon-button-hover-state-layer-opacity: 1;
    --mat-icon-button-focus-state-layer-opacity: 1;
    --mat-icon-button-pressed-state-layer-opacity: 1;
    --mat-icon-button-state-layer-color:transparent;
    ::ng-deep .mat-mdc-button-persistent-ripple.mdc-icon-button__ripple {
        border-radius: inherit;
    }
    ::ng-deep .mat-mdc-button-persistent-ripple.mdc-icon-button__ripple::before {
        border-radius: inherit;
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.20) 0%, rgba(0, 0, 0, 0.20) 100%);
        transition: opacity 0.5s ease;
    }
}