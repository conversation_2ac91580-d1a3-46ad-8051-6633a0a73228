import { ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { PageEvent} from '@angular/material/paginator';
import { FormControl, FormGroup } from '@angular/forms';
import moment, { Moment } from 'moment';
import { provideMomentDateAdapter } from '@angular/material-moment-adapter';
import { RoutingService } from '../../../services/routing-service';
import { CustomCalendarHeaderComponent } from '../../../components/custom-calendar-header/custom-calendar-header.component';
import 'moment/locale/es';
import { SearchReproductionsComplex, SearchReproductionsSimple, Song, SongReproductionsDTO } from '../../../types/reproductions';
import { ReproductionsService } from '../../../services/reproductions-service';
import { debounceTime } from 'rxjs';
import { mkConfig, generateCsv, download } from "export-to-csv";
import { CARRIERS } from '../../../types/api';
import { MatMultiSort, MatMultiSortTableDataSource, TableData } from 'ngx-mat-multi-sort';
import { abbreviateNumber } from "js-abbreviation-number";
import { SearchService } from '../../../services/search-service';
import { asOption } from '../../../types/search';
import { MatDialog } from '@angular/material/dialog';
import { MultitargetSearchDialogComponent } from '../../../components/multitarget-search-dialog/multitarget-search-dialog.component';

@Component({
  selector: 'app-reproductions',
  templateUrl: './reproductions.component.html',
  styleUrl: './reproductions.component.css',
  providers: [
    provideMomentDateAdapter({
      parse: {
        dateInput: ['l', 'LL'],
      },
      display: {
        dateInput: 'DD / MM / YYYY',
        monthYearLabel: 'MMM YYYY',
        dateA11yLabel: 'LL',
        monthYearA11yLabel: 'MMMM YYYY',
      },
    }),
  ],
})
export class ReproductionsComponent implements OnInit {
  @ViewChild('searchDisplayButton', { static: false }) public dialogDisplayButtonRef!: ElementRef<HTMLButtonElement>;
  maxDate: Moment;
  usedFilters: string[] = [];
  
  csvConfig = mkConfig({ columnHeaders:[
    {key:'artist', displayLabel:'Artista'},
    {key:'song',displayLabel:'Canción'},
    {key:'album',displayLabel:'Albúm'},
    {key:'gender',displayLabel:'Género'},
    {key:'date',displayLabel:'Decada'},
    {key:'recordLabel',displayLabel:'Disquera'},
    {key:'reproductions',displayLabel:'Reproducciones'},
  ] });
  baseData!: Song[];
  table: TableData<Song>;
  @ViewChild(MatMultiSort) sort!: MatMultiSort;
  constructor(
    private routingService: RoutingService,
    private reproductionsService: ReproductionsService,
    private searchService: SearchService,
    private changeDetectorRef: ChangeDetectorRef,
    private searchDialog: MatDialog
  ) {
    this.maxDate = moment().subtract(1, 'd');
    this.table = new TableData<Song>(
      [
        { id: 'artist', name: 'Artista' },
        { id: 'song', name: 'Canción' },
        { id: 'album', name: 'Albúm' },
        { id: 'gender', name: 'Género' },
        { id: 'decade', name: 'Decada' },
        { id: 'recordLabel', name: 'Disquera' },
        { id: 'reproductions', name: 'Reproducciones' },
      ], { localStorageKey: 'settings' }
    ); 
  }

  customHeader = CustomCalendarHeaderComponent;
  carrierOptions = [
    { label: 'Todos', value: null },
    { label: 'Movistar', value: CARRIERS.MOV },
    { label: 'Digitel', value: CARRIERS.DIG },
  ];

  reproductionsSearchForm = new FormGroup({
    carrierOption: new FormControl<CARRIERS | null>(
      this.carrierOptions[0].value
    ),
    initDate: new FormControl<Moment>(
      moment().subtract(1, 'd').subtract(1, 'month')
    ),
    limitDate: new FormControl<Moment | null>(null),
  });

  openSearchDialog() {
    const btnRef: DOMRect = this.dialogDisplayButtonRef.nativeElement.getBoundingClientRect();
    const dialogRef = this.searchDialog.open(MultitargetSearchDialogComponent,{
      hasBackdrop:true,
      position:{
        top:`${btnRef.top + btnRef.height + 15}px`,
        left:`${btnRef.left}px`
      },
      backdropClass:'non-opacity-background'
    });
    const sub = dialogRef.componentInstance.onOptionClick.subscribe((data: asOption) => {
      this.addFilterValue(data.nombre);
    });
  }

  invokeNavigateTo(url: string) {
    this.routingService.add(url);
  }
  addFilterValue(newFilter: string) {
    if(this.usedFilters.includes(newFilter) === false){
      this.usedFilters.push(newFilter);
      this.getReproductionsData(this.getSearchParams());
    }
  }
  removeFilterValue(index: number) {
    this.usedFilters = this.usedFilters.filter((e, ind) => ind !== index);
    this.getReproductionsData(this.getSearchParams());
  }
  clearFilters() {
    this.usedFilters = [];
    this.getReproductionsData(this.getSearchParams());
  }
  isSortingActive(sort:string): boolean {
    return this.table.sortParams.includes(sort);
  }
  getSortingDirection(sort:string): string {
    return this.table.sortDirs[this.table.sortParams.findIndex((e) => sort === e)];
  }
  quantityParser(qty:string){
    return abbreviateNumber(parseInt(qty),1);
  }
  exportData() {
    const {nameFilters,...searchParams} = this.getSearchParams();
    if(nameFilters.length > 0){
      this.reproductionsService
        .getReproductionsDataWithFilters({nameFilters,...searchParams})
        .subscribe({
          next: (res) => {
            const { songs } = (res.body as SongReproductionsDTO).statistics;
            const asData: any = this.sortTableData(songs,this.table.sortParams,this.table.sortDirs).map(({date,...r}) => ({...r,date:this.parseDecade(date,'YYYY')}));
            download({...this.csvConfig,filename:`REPRODUCCIONES_AQUSTICO`})(generateCsv(this.csvConfig)(asData));
          },
          error: (err) => {
            //console.log(err);
          },
        });
    } else if (nameFilters.length === 0) {
      this.reproductionsService.getReproductionsData({...searchParams,page:0,take:this.table.totalElements} as SearchReproductionsSimple).subscribe({
        next: (res) => {
          const { songs } = (res.body as SongReproductionsDTO).statistics;
          const asData: any = this.sortTableData(songs,this.table.sortParams,this.table.sortDirs).map(({date,...r}) => ({...r,date:this.parseDecade(date,'YYYY')}));
          download({...this.csvConfig,filename:`REPRODUCCIONES_AQUSTICO`})(generateCsv(this.csvConfig)(asData));
        },
        error: (err) => {
          console.log(err);
        },
      });
    }
  }

  getSearchParams(): SearchReproductionsComplex {
    return {
      page: this.table.pageIndex ? this.table.pageIndex:0,
      take: this.table.pageSize ? this.table.pageSize: 5,
      carrier: this.reproductionsSearchForm.controls.carrierOption.value,
      startDate:
        this.reproductionsSearchForm.controls.initDate.value?.format(
          'YYYY-MM-DD'
        ),
      endDate:
        this.reproductionsSearchForm.controls.limitDate.value === null
          ? moment().subtract(1, 'd').format('YYYY-MM-DD')
          : this.reproductionsSearchForm.controls.limitDate.value.format(
              'YYYY-MM-DD'
            ),
      nameFilters: this.usedFilters,
    };
  }

  _sortData(d1: Song, d2: Song, sorting: string[], dirs: string[]): number {
    // @ts-ignore -- need a typesafe way to express these accessor operations, ts-ignore could be a solution
    // if there's not a suitable solution offered by typescript
    if (d1[sorting[0]] > d2[sorting[0]]) {
      return dirs[0] === 'asc' ? 1 : -1;
      // @ts-ignore
    } else if (d1[sorting[0]] < d2[sorting[0]]) {
      return dirs[0] === 'asc' ? -1 : 1;
    } else {
      if (sorting.length > 1) {
        sorting = sorting.slice(1, sorting.length);
        dirs = dirs.slice(1, dirs.length);
        return this._sortData(d1, d2, sorting, dirs);
      } else {
        return 0;
      }
    }
  }

  public parseDecade(d: string, asFormat: string) {
    return moment(d).format(asFormat);
  }

  ngOnInit() {
    this.reproductionsSearchForm.valueChanges.pipe(debounceTime(500)).subscribe(() => {
      this.getReproductionsData(this.getSearchParams());
    });
    this.changeDetectorRef.detectChanges();
    this.initTableData();
  }

  sortTableData(initData:Song[],sortings:string[],dirs:string[]) {
    const tempData = Object.assign([], initData);
      let result: Song[] = [];
      if (sortings.length === 0) {
        result = initData;
      } else if (sortings.length > 0) {
        const sortedData = tempData.sort((u1, u2) => {
          return this._sortData(u1, u2, sortings, dirs);
        });
        result = sortedData;
      }
    return result;
  }

  initTableData(){
    this.table.dataSource = new MatMultiSortTableDataSource<Song>(this.sort,false);
    this.usedFilters = this.searchService.getQuickSearch();
    this.table.sortObservable.subscribe(() => {
      this.table.data = this.sortTableData(this.baseData,this.table.sortParams,this.table.sortDirs);
    });
    this.getReproductionsData(this.getSearchParams());
  }

  onPageChange(e: PageEvent) {
    this.table.totalElements = e.length;
    this.table.pageIndex = e.pageIndex;
    this.table.pageSize = e.pageSize;
    this.getReproductionsData(this.getSearchParams());
  }

  refreshTableData({statistics}:SongReproductionsDTO) {
    const { songs, total } = statistics;
    this.table.totalElements = total;
    this.baseData = songs;
    this.table.data = this.sortTableData(this.baseData,this.table.sortParams,this.table.sortDirs);;
  }

  getReproductionsData({nameFilters,...searchParams}: SearchReproductionsComplex) {
    //ERROR ANTE INGRESO DE CADENA ' '
    if (nameFilters.length > 0) {
      this.reproductionsService
        .getReproductionsDataWithFilters({nameFilters,...searchParams})
        .subscribe({
          next: (res) => {
            this.refreshTableData(res.body as SongReproductionsDTO);
          },
          error: (err) => {
            //console.log(err);
          },
        });
    } else {
      this.reproductionsService
        .getReproductionsData(searchParams as SearchReproductionsSimple)
        .subscribe({
          next: (res) => {
            this.refreshTableData(res.body as SongReproductionsDTO);
          },
          error: (err) => {
            //console.log(err);
          },
        });
    }
  }
}
  