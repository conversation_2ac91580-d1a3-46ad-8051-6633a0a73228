<mat-tab-group #radioFormTabs animationDuration="0ms" disablePagination="true">
    <mat-tab>
        <ng-template mat-tab-label>
            <div class="tab-header">
                <mat-icon class="material-symbols-outlined tab-header-icon">{{ selectedTabIndex === 0 ? 'counter_1':'check_circle' }}</mat-icon>
                Información Radio
            </div>
        </ng-template>
        <form [formGroup]="radioForm" class="tab-content" style="gap: 28px;" #formStep1>
            <div class="radio-cover-field" (click)="filePicker.click()">
                <div class="radio-cover-preview" (mouseover)="mouseEnter()" (mouseout)="mouseLeave()">
                    <mat-icon *ngIf="asRadioCover === null" [style.opacity]="isRadioCoverFieldHovered ? 0:1"
                        class="material-symbols-outlined">
                        imagesmode
                    </mat-icon>
                    <mat-icon *ngIf="asRadioCover === null" [style.opacity]="isRadioCoverFieldHovered ? 1:0"
                        class="material-symbols-outlined">
                        upload_2
                    </mat-icon>
                    <img *ngIf="asRadioCover !== null" [src]="asRadioCover" alt="Radio Cover" />
                </div>
                <h5>Añadir Cover</h5>
                <h6>Medidas 1080 x 720px</h6>
                <input type="file" #filePicker (change)="onImagePicked($event)" hidden>
            </div>
            <div class="fields-container">
                <div class="radio-info-field-container">
                    <div class="radio-info-field-title">
                        <p>Título*</p>
                        <p>(Máximo 22 caracteres)</p>
                    </div>
                    <mat-form-field appearance="outline" class="radio-info-field" subscriptSizing="dynamic">
                        <input matInput [formControl]="radioForm.controls.title"
                            placeholder="Ingresa nombre de la radio" maxlength="22" />
                    </mat-form-field>
                </div>
                <div class="radio-info-field-container">
                    <div class="radio-info-field-title">
                        <p>Autor*</p>
                        <p>(Máximo 22 caracteres)</p>
                    </div>
                    <mat-form-field class="radio-info-field" appearance="outline" subscriptSizing="dynamic">
                        <input matInput [formControl]="radioForm.controls.author" placeholder="Ingresa nombre del autor"
                            maxlength="22" />
                    </mat-form-field>
                </div>
            </div>
        </form>
    </mat-tab>
    <mat-tab [disabled]="!radioForm.valid">
        <ng-template mat-tab-label>
            <div class="tab-header">
                <mat-icon class="material-symbols-outlined tab-header-icon">{{ selectedTabIndex === 1 ? 'counter_2':'check_circle' }}</mat-icon>
                Añadir Canciones
            </div>
        </ng-template>
        <form [formGroup]="searchGroup" class="tab-content" class="tab-content advanced" #formStep2>
            <div class="radio-identifier">
                <h3>{{radioForm.controls.title.value}}</h3>
                <h4>{{radioForm.controls.author.value}}</h4>
            </div>
            <div class="radio-config-selector-container">
                <mat-form-field subscriptSizing="dynamic" class="lookup-field-commons lookup-field-selector">
                    <mat-select disableRipple hideSingleSelectionIndicator="true"
                        [formControl]="searchGroup.controls.lookupType" [panelClass]="['lookup-panel']">
                        @for (lookupType of lookupTypes; track lookupType) {
                        <mat-option [value]="lookupType.value" class="lookup-option">{{lookupType.label}}</mat-option>
                        }
                    </mat-select>
                </mat-form-field>
                <mat-form-field subscriptSizing="dynamic" class="lookup-field-commons lookup-field-input">
                    <input matInput type="text" placeholder="Buscar género, artista, canciones..."
                        [formControl]="searchGroup.controls.searchControl">
                    <button matPrefix mat-icon-button aria-label="search-indicator">
                        <mat-icon>search</mat-icon>
                    </button>
                    @if(searchGroup.controls.searchControl.value !== '') {
                    <button matSuffix mat-icon-button aria-label="Clear" (click)="onClearSearch()">
                        <span class="material-symbols-outlined">cancel</span>
                    </button>
                    }
                </mat-form-field>
            </div>
            <div class="lookup-elements">
                @if(searchOptions !== null && isLoading === false) {
                    @for (lookupOption of searchOptions; track lookupOption; let i = $index) {
                    <app-lookup-config-item [lookupOption]="lookupOption" (update_selections)="updateSelections($event)"
                        [is_selected]="lookupTypeConfigMap[lookupOption.optionType].includes(lookupOption.optionId)" />
                    }
                }
                @else if (searchOptions === null && isLoading === false) {
                    @if(searchGroup.controls.searchControl.value !== '') {
                    <div class="lookup-elements-empty">
                        <mat-icon class="material-symbols-outlined">search_off</mat-icon>
                        <h5>No se encontraron coincidencias.<br>Intenta de nuevo.</h5>
                    </div>
                }
                @else {
                    <div class="lookup-elements-empty">
                        <mat-icon class="material-symbols-outlined">list_alt_add</mat-icon>
                        <h5>Aún no has agregado contenido, usa el buscador para encontrarla.</h5>
                    </div>
                    }
                }

            </div>
        </form>
    </mat-tab>
    <mat-tab [disabled]="searchGroup.pristine || !searchGroup.valid">
        <ng-template mat-tab-label>
            <div class="tab-header">
                <mat-icon class="material-symbols-outlined tab-header-icon">{{ selectedTabIndex === 2 ? 'counter_3':'check_circle' }}</mat-icon>
                Añadir Anunciantes
            </div>
        </ng-template>
        <form [formGroup]="adsSearch" class="tab-content" class="tab-content advanced" #formStep3>
            <div class="radio-identifier">
                <h3>{{radioForm.controls.title.value}}</h3>
                <h4>{{radioForm.controls.author.value}}</h4>
            </div>
            <div class="radio-config-selector-container">
                <mat-form-field subscriptSizing="dynamic" class="lookup-field-commons lookup-field-input">
                    <input matInput type="text" placeholder="Buscar anuncio"
                        [formControl]="adsSearch.controls.adsControl">
                    <button matPrefix mat-icon-button aria-label="search-indicator">
                        <mat-icon>search</mat-icon>
                    </button>
                    @if(adsSearch.controls.adsControl.value !== '') {
                    <button matSuffix mat-icon-button aria-label="Clear" (click)="onAdsClearSearch()">
                        <span class="material-symbols-outlined">cancel</span>
                    </button>
                    }
                </mat-form-field>
            </div>
            <div class="lookup-elements">
                @if(searchOptionsPlatformMedia !== null && isLoading === false) {
                @for (lookupOption of searchOptionsPlatformMedia; track lookupOption; let i = $index) {
                <app-lookup-config-item [lookupOption]="lookupOption" (update_selections)="updateAdsSelections($event)"
                    [is_selected]="lookupTypeMapPlatformMedia.includes(lookupOption.optionId)" />
                }
                }
                @else if (searchOptionsPlatformMedia === null && isLoading === false) {
                @if(adsSearch.controls.adsControl.value !== '') {
                <div class="lookup-elements-empty">
                    <span class="material-icons">search_off</span>
                    <h5>No se encontraron coincidencias.<br>Intenta de nuevo.</h5>
                </div>
                }
                @else {
                <div class="lookup-elements-empty">
                    <mat-icon class="material-symbols-outlined">list_alt_add</mat-icon>
                    <h5>Aún no has agregado contenido, usa el buscador para encontrarla.</h5>
                </div>
                }
                }
            </div>
        </form>
    </mat-tab>
    <mat-tab [disabled]="!searchGroup.valid || !adsSearch.valid">
        <ng-template mat-tab-label>
            <div class="tab-header">
                <mat-icon class="material-symbols-outlined tab-header-icon">{{ selectedTabIndex === 3 ? 'counter_4':'check_circle' }}</mat-icon>
                Añadir Micros
            </div>
        </ng-template>
        <form [formGroup]="contentSearch" class="tab-content" class="tab-content advanced" #formStep3>
            <div class="radio-identifier">
                <h3>{{radioForm.controls.title.value}}</h3>
                <h4>{{radioForm.controls.author.value}}</h4>
            </div>
            <div class="radio-config-selector-container">
                <mat-form-field subscriptSizing="dynamic" class="lookup-field-commons lookup-field-input">
                    <input matInput type="text" placeholder="Buscar micro..."
                        [formControl]="contentSearch.controls.contentControl">
                    <button matPrefix mat-icon-button aria-label="search-indicator">
                        <mat-icon>search</mat-icon>
                    </button>
                    @if(contentSearch.controls.contentControl.value !== '') {
                    <button matSuffix mat-icon-button aria-label="Clear" (click)="onContentClearSearch()">
                        <span class="material-symbols-outlined">cancel</span>
                    </button>
                    }
                </mat-form-field>
            </div>
            <div class="lookup-elements">
                @if(searchOptionsContentMedia !== null && isLoading === false) {
                @for (lookupOption of searchOptionsContentMedia; track lookupOption; let i = $index) {
                    <app-lookup-config-item 
                        [lookupOption]="lookupOption"
                        (update_selections)="updatePlatformContentSelections($event)"
                        [is_selected]="lookupTypeMapContentMedia.includes(lookupOption.optionId)" 
                    />
                }
                }
                @else if (searchOptionsContentMedia === null && isLoading === false) {
                @if(contentSearch.controls.contentControl.value !== '') {
                <div class="lookup-elements-empty">
                    <span class="material-icons">search_off</span>
                    <h5>No se encontraron coincidencias.<br>Intenta de nuevo.</h5>
                </div>
                }
                @else {
                <div class="lookup-elements-empty">
                    <mat-icon class="material-symbols-outlined">list_alt_add</mat-icon>
                    <h5>Aún no has agregado contenido, usa el buscador para encontrarla.</h5>
                </div>
                }
                }
            </div>
        </form>
    </mat-tab>
    <mat-tab [disabled]="!searchGroup.valid || !adsSearch.valid || !contentSearch.valid">
        <ng-template mat-tab-label>
            <div class="tab-header">
                <mat-icon class="material-symbols-outlined tab-header-icon">counter_5</mat-icon>
                Resumen
            </div>
        </ng-template>
        <div class="tab-content preview-stage">
            <h3>Resumen</h3>
            <div class="preview-container">
                <img [src]="asRadioCover" width="240" height="160" />
                <div class="preview-summary">
                    <div class="preview-summary-element">
                        <h5>Título</h5>
                        <h4>{{radioForm.controls.title.value}}</h4>
                    </div>
                    <div class="preview-summary-element">
                        <h5>Autor</h5>
                        <h4>{{radioForm.controls.author.value}}</h4>
                    </div>
                    <div class="preview-summary-element">
                        <h5>Géneros</h5>
                        <div class="summary-sub-element">
                            @for(option of lookupTypeConfigMapSummary[lookupTypeEnum.GENRE]; track option; let i = $index) {
                            <h4>{{ ( i !== 0 ? ', ':'' ) + option.name}}</h4>
                            }
                        </div>
                    </div>
                    <div class="preview-summary-element">
                        <h5>Artistas</h5>
                        <div class="summary-sub-element">
                            @for(option of lookupTypeConfigMapSummary[lookupTypeEnum.ARTIST]; track option; let i = $index) {
                            <h4>{{ ( i !== 0 ? ', ':'' ) + option.name}}</h4>
                            }
                        </div>
                    </div>
                    <div class="preview-summary-element">
                        <h5>Canciones</h5>
                        <h4>{{searchGroup.controls.contentConfig.value!.songIds!.length}} canciones</h4>
                    </div>
                </div>
            </div>
        </div>
    </mat-tab>
</mat-tab-group>
<div class="form-actions">
    @if(selectedTabIndex > 0){
    <button type="button" mat-flat-button class="common-flat-button plain on-hover-flat-button"
        (click)="onBackAction(selectedTabIndex)"
        [disabled]="isSaving">
        {{selectedTabIndex === 4 ? 'Guardar':'Atrás'}}
    </button>
    }
    <button type="button" mat-flat-button (click)="stageSubmit(selectedTabIndex)"
        class="common-flat-button on-hover-flat-button common-flat-button-disabled"
        [disabled]="!radioForm.valid || (!searchGroup.valid && selectedTabIndex === 1) || (!searchGroup.valid && selectedTabIndex === 2) || (!adsSearch.valid && selectedTabIndex === 3) || (!contentSearch.valid && selectedTabIndex === 3) || isSaving">
        {{selectedTabIndex === 4 ? 'Publicar':'Siguiente'}}
    </button>
</div>