import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { AUTH_USER } from '../types/auth';

@Injectable({
    providedIn: 'root',
})
export class AuthService {
  constructor(private http:HttpClient){}

  private apiUrl = environment.apiUrl;
  private serviceUrl = `${this.apiUrl}/auth`;

  public authUser(authData:{email:string,password:string}){
    return this.http.post<AUTH_USER>(`${this.serviceUrl}/login`,authData,{ observe:"response", responseType:"json" },)
  }
}