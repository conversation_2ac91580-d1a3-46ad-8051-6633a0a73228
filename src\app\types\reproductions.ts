import { ResponseDTO } from "./api";

export interface Song {
    album:string;
    id_album:number;
    song:string;
    id_song:number;
    gender:string;
    date:string;
    duration?:string;
    reproductions:number;
    tendency:number;
}

export interface SongPlayback {
    album:string;
    id_album:number;
    song:string;
    id_song:number;
    gender:string;
    date:string;
    reproductions:number;
    recordlabel:string;
    previous_reproductions:number;
}

export interface SongReproductionsData {
    songs:Song[];
    total:number;
}

export interface SongReproductionsDTO extends ResponseDTO {
    statistics:SongReproductionsData;
}

export interface SearchReproductionsComplex extends SearchReproductionsSimple, SearchReproductionsFilters {}

export interface SearchReproductionsSimple {
    carrier?:number | null;
    startDate?:string;
    endDate?:string;
    albumId?:number;
    take?:number;
    page?:number;
}

export interface SearchReproductionsFilters {
    nameFilters:string[];
}

export interface ReproductionByPeriod {
    total:number;
    _date:string;
}

export interface SongPlayback {
    album:string;
    id_album:number;
    song:string;
    id_song:number;
    gender:string;
    date:string;
    reproductions:number;
    recordlabel:string;
    previous_reproductions:number;
}