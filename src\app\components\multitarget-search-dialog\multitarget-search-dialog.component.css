@import './../multitarget-autocomplete.component.css';

/**/
.search-input {
    --mdc-filled-text-field-input-text-color: rgb(0, 0, 0);
    --mat-form-field-container-text-size: 18px;
    --mat-form-field-container-text-line-height: 24px;
    --mat-form-field-container-height: 28px;
    --mat-form-field-container-text-weight: 500;
    --mat-form-field-container-vertical-padding: 20px;
    --as-border-color: transparent;
    --mdc-filled-text-field-hover-outline-color: var(--as-border-color);
    --mdc-filled-text-field-outline-color: var(--as-border-color);
    --mdc-filled-text-field-focus-outline-color: var(--as-border-color);
    --mdc-filled-text-field-input-text-placeholder-color: rgb(146, 146, 146);
    --mdc-filled-text-field-caret-color: rgb(0, 0, 0);
    --as-background-color:#f2f2f2;
    border-radius: 8px;
    background-color: #f2f2f2;
    --mdc-filled-text-field-container-color:var(--as-background-color) ;
    --mdc-filled-text-field-container-shape: 12px;
    --mat-form-field-hover-state-layer-opacity: 0.00;
    --mat-form-field-focus-state-layer-opacity: 0.00;
    
    --active-indicator-color:rgba(104, 109, 118, 0.6);
    --mdc-filled-text-field-focus-active-indicator-color:var(--active-indicator-color);
    --mdc-filled-text-field-active-indicator-color:var(--active-indicator-color);
    --mdc-filled-text-field-hover-active-indicator-color:var(--active-indicator-color);
    --mdc-filled-text-field-focus-active-indicator-height:1px;

    font-family: "Visby CF";
    color: black;
    width: 304px;
    font-size: 18px;
    font-weight: 600;
  }

  .search-input-icon {
    --icon-dim:20px;
    height: var(--icon-dim);
    width: var(--icon-dim);
    font-size: var(--icon-dim);
    padding: 0px 8px 0px 8px;
  }
/**/