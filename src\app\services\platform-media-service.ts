import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { PlatformMedia, PlatformMediaType } from '../types/platform-media';
import { Observable } from 'rxjs';

@Injectable({
    providedIn: 'root',
})
export class PlatformMediaService {
    constructor(private http:HttpClient){}
    
    private apiUrl = environment.apiUrl;
    private serviceUrl = `${this.apiUrl}/platform-media`;

    getPlatformMedia(search:string, type:PlatformMediaType = PlatformMediaType.PLATFORM_CONTENT) {
        return this.http.get<PlatformMedia[]>(`${this.serviceUrl}/by-type`, { observe: 'response', responseType: 'json', params: { search, type } });
    }

    //getPlatformMediaByType(type : string | PlatformMediaType) {
    //    return this.http.get<PlatformMedia[]>(`${this.serviceUrl}/all-by-type`, { observe: 'response', responseType: 'json', params: { type } });
    //}

    getPlatformMediaByType(types: PlatformMediaType | PlatformMediaType[]): Observable<any> {
        const typeArray = Array.isArray(types) ? types : [types];
        
        const typeString = typeArray.join(',');
        
        return this.http.get<PlatformMedia[]>(`${this.serviceUrl}/all-by-type`, { 
            observe: 'response', 
            responseType: 'json', 
            params: { type: typeString } 
        });
    }
}