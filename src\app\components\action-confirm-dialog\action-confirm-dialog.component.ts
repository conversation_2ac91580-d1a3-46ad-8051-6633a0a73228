import { Component, EventEmitter, Output } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-action-confirm-dialog',
  templateUrl: './action-confirm-dialog.component.html',
  styleUrl: './action-confirm-dialog.component.css'
})
export class ActionConfirmDialogComponent {
  constructor(
    private _dialogRef: MatDialogRef<ActionConfirmDialogComponent>,
  ) {}

  @Output() onActionClick = new EventEmitter<boolean>();

  actionClickHandle(op:boolean) {
    this.onActionClick.emit(op);
    this.onClose();
  }

  onClose() {
    this._dialogRef.close(true);
  }
}
