<style>
     .custom-calendar-header {
        display: flex;
        width:291px;
        align-items: center;
        padding: 0.5em;
        justify-content:space-between;
        --mat-datepicker-calendar-date-selected-state-background-color:#550DC5;
        --mat-datepicker-calendar-date-selected-state-text-color:white;
      }
      .custom-header-label {
        width:fit-content;
        color:#201F28;
        text-align: center;
        font-family: Visby CF;
        font-size: 18px;
        font-weight: 600;
        line-height: 28px;
        letter-spacing: 0em;
        text-transform: capitalize;
      }
      .header-button {
        color:#550DC5;
      }
      .header-button > mat-icon {
        width: 24px;
        height: 24px;
      }
      :host {
        display: flex;
        justify-content: center;
      }
</style>
<div class="custom-calendar-header">
    <button mat-icon-button (click)="previousClicked('month')" class="header-button" variant="raised">
      <mat-icon>keyboard_arrow_left</mat-icon>
    </button>
    <span class="custom-header-label">{{ periodLabel }}</span>
    <button mat-icon-button (click)="nextClicked('month')" class="header-button">
      <mat-icon>keyboard_arrow_right</mat-icon>
    </button>
</div>