<div>
<p><PERSON><PERSON> canciones</p>
</div>

<div 
  class="file-upload-container" 
  (drop)="onFileDropped($event)" 
  (dragover)="onDragOver($event)"
>

@switch (status) {
  @case ('loading') {
    <mat-icon svgIcon="loaderarchivo_icon" class="upload-icon"></mat-icon>
  }
  @case ('success') {
    <mat-icon svgIcon="cargaarchivo_icon" class="upload-icon"></mat-icon>
  }
  @case ('error') {
    <p>Ocurrió un error al cargar.</p>
  }
  @case ('upfile') {
    <mat-icon svgIcon="loaderarchivo_icon" class="upload-icon"></mat-icon>
  }
  @case ('uploading') {
    <mat-icon svgIcon="loaderarchivo_icon" class="upload-icon"></mat-icon>
  }
  @case ('successfile') {
    <mat-icon svgIcon="checketarchivo_icon" class="upload-icon"></mat-icon>
  }
  @case ('errorupfile') {
    <mat-icon svgIcon="cancelarchivo_icon" class="upload-icon"></mat-icon>
  }
  @default {
    <mat-icon svgIcon="uploadbtn_icon" class="upload-icon"></mat-icon>
  }
}


<span class="upload-texto espacio">{{textup1}}</span>
<span class="upload-texto espacio">{{textup2}}</span>
<span class="upload-texto espacio"></span>

<audio id="upload-audio" preload="metadata"></audio>

@if (status !== 'success' && status !== 'uploading' && status !== 'loading' && status !== 'errorupfile') {

  <input type="file" id="fileUpload" #fileUpload (change)="onFilesSelected($event)" accept=".csv" style="display: none;" />


  <label for="fileUpload" class="file-upload-label">{{boton}}</label>

}

@if (status === 'success' || status === 'errorupfile') {
  <input type="button" id="newButton" (click)="onUploadClick()" style="display: none;" />
  <label for="newButton" class="file-upload-label">{{boton2}}</label>
}



</div>

  

