<div mat-dialog-container [class]="options.length === 0?'':'search-positive'">
  <mat-dialog-content class="search-dialog">
    <form [formGroup]="searchGroup" class="search-form">
      <mat-form-field subscriptSizing="dynamic" class="search-input" appearance="fill">
        <mat-icon matPrefix class="search-input-icon">search</mat-icon>
        <input matInput type="text" placeholder="Buscar..." [formControl]="searchGroup.controls.searchControl"
          [matAutocomplete]="auto" />
        <mat-icon matSuffix class="search-input-icon" (click)="resetSearch()">close</mat-icon>
        <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayFn"
          showPanel="false" class="search-options-ac">
          @for (option of searchOptions | async; track option) {
            <mat-option [value]="option" (click)="invokeAction(option)">
              <div class="songs-autocomplete-item">
                <ng-template #asSearch>
                  <div style="display: flex; align-items: center; justify-content: center;width: 32px;height: 32px;">
                    <mat-icon style="margin-right: 0px;">
                      search
                    </mat-icon>
                  </div>
                </ng-template>
                <ng-template #asAlbumImg let-albumImg="albumImg">
                  <ng-container *ngIf="albumImg!==undefined;else asSearch">
                    <img [ngSrc]="albumImg" width="32" height="32" />
                  </ng-container>
                </ng-template>
                <ng-container [ngTemplateOutlet]="option.as >= 1 ? asAlbumImg:asSearch" [ngTemplateOutletContext]="{
                    albumImg:getAlbumImg(option)
                  }"></ng-container>
                <div class="songs-autocomplete-item-text">
                  <ng-template #highlightItemTitle let-asText="asText">
                    <h5 [innerHtml]="asText"></h5>
                  </ng-template>
                  <ng-template #basicItemTitle let-asText="asText">
                    <h5 style="font-weight: 600;">{{asText}}</h5>
                  </ng-template>
                  <ng-container [ngTemplateOutlet]="option.as === 0 ? highlightItemTitle:highlightItemTitle" [ngTemplateOutletContext]="{
                    asText:option.matchTag
                  }">
                  </ng-container>
                  <h6>{{OptionTypeParse(option.as)}}</h6>
                </div>
              </div>
            </mat-option>
            }
        </mat-autocomplete>
      </mat-form-field>
      <div *ngIf="options.length === 0" style="width: 100%;text-align: center;padding: 10px 0px;height: 32px;">
        <h4 style="margin: 0px;">No hay resultados</h4>
      </div>
    </form>
  </mat-dialog-content>
</div>