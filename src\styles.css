/* You can add global styles to this file, and also import other style files */

html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }

@font-face {
    font-family: 'Visby SemiBold CF';
    src: url('assets/fonts/VisbySemibold.woff') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Visby CF';
    src: url('assets/fonts/VisbyRegular.woff') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Visby Semibold';
    src: url('assets/fonts/VisbySemibold.woff') format('truetype');
}

@font-face {
    font-family: 'Noto Sans';
    src: url('assets/fonts/NotoSans-SemiBold.ttf') format('truetype');

}

@font-face {
    font-family: 'Inter';
    src: url('assets/fonts/Inter-VariableFont_slnt\,wght.ttf') format('truetype');

}

.error-snackbar {
    border-radius: 12px !important;
    margin-top: 25px !important;
    --mdc-snackbar-container-color:#FF003F;
    --mdc-snackbar-container-shape:12px;
}

.success-snackbar {
    border-radius: 12px !important;
    margin-top: 25px !important;
    --mdc-snackbar-container-color:#0fb300;
    --mdc-snackbar-container-shape:12px;
}

.process-snackbar {
    --mdc-snackbar-container-color:#FFF;
    --mdc-snackbar-container-shape:8px;
}

.process-snackbar > div.mdc-snackbar__surface {
    min-width: 270px;
}

.mdc-tooltip__surface {
    max-width: max-content !important;
}