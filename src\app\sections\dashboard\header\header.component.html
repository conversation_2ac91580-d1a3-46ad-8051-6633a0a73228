<div
  style="
    width: 100%;
    display: flex;
    justify-content: space-between;
    height: 100%;
  "
>
  <div style="display: flex; align-items: center; height: 100%">
    @if(rootIcon) {
      <mat-icon class="header-icon">{{rootIcon}}</mat-icon>
    }
    @else {
      <mat-icon svgIcon="header_icon" class="header-icon"></mat-icon>
    }
    <div style="display: flex">
      <ng-template ngFor let-item [ngForOf]="breadcrumbElements" let-i="index">
        <h4 class="header-breadcrumb-item header-breadcrumb-item-animation header-separator" *ngIf="i !== 0">/</h4>
        <h4 class="header-breadcrumb-item header-breadcrumb-item-animation" [class.header-breadcrumb-item-highlighted]="(i !== breadcrumbElements.length - 1) || isSpecialSectionActive">{{ item.asName }}</h4>
      </ng-template>
    </div>
  </div>
  <div style="display: flex; align-items: center">
    <button *ngIf="activateCustomSearch" (click)="openSearchDialog()" #searchDisplayButton class="search-display-button">
      <mat-icon class="search-display-icon">search</mat-icon>
      <p class="search-display-button-text">Buscar artista, álbum, canción....</p>
    </button>
    <mat-icon svgIcon="bell_icon" style="margin-right: 65px"></mat-icon>
  </div>
</div>

