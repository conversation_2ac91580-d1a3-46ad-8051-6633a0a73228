import { Component } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { AUTH_USER, User } from '../../types/auth';
import { RoutingService } from '../../services/routing-service';
import { AuthService } from '../../services/auth-service';
import { UserProfileService } from '../../services/user-profile-service';
import { CustomSnackbarComponent } from '../../components/custom-snackbar/custom-snackbar.component';
import { MatSnackBar } from '@angular/material/snack-bar';
@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrl: './login.component.css',
})
export class LoginComponent {
  constructor(
    private routingService: RoutingService,
    private authService: AuthService,
    private userProfileService: UserProfileService,
    private _snackBar: MatSnackBar
  ) {}

  durationInSeconds = 10;

  openSnackBar() {
    this._snackBar.openFromComponent(CustomSnackbarComponent, {
      duration: this.durationInSeconds * 1000,
      horizontalPosition:"center",
      verticalPosition:"top",
      data:{ message:'Datos inválidos. Por favor, reintente' },
      panelClass:['error-snackbar'],
    });
  }

  showPassword: boolean = false;
  loginForm = new FormGroup(
    {
      email: new FormControl('',[
        Validators.required,
        Validators.pattern(/^([^ ()<>@,;:"\[\]ç%&A-Z\u00C0-\u00FF])+@([a-z]*\.(?!$))+([a-z]*$)/),
      ]),
      password: new FormControl('',[
        Validators.required,
        Validators.minLength(8),
        Validators.pattern(/([A-Z]{1,})/),
        Validators.pattern(/([0-9]{1,})/),
        Validators.pattern(/([@_\-#\$&\*\/!\?]{1,})/),
        Validators.pattern(/^[^\u00C0-\u00FF]*$/)
      ]),
    },
    { updateOn: 'submit' }
  );

  onSubmit() {
    this.loginForm.setErrors(null);
    const { email, password } = this.loginForm.value;
    if (this.loginForm.valid) {
      this.authService
        .authUser({ email: email as string, password: password as string })
        .subscribe({
          next: (val) => {
            const { access_token, ...restBody } = val.body as AUTH_USER;
            this.userProfileService.addProfile(
              restBody as User,
              access_token
            );
            this.routingService.add('dashboard/');
          },
          error: (err) => {
            this.openSnackBar();
            this.loginForm.markAsDirty();
            this.loginForm.controls.email.setErrors({ invalidAuth:true });
            this.loginForm.controls.password.setErrors({ invalidAuth:true });
            this.loginForm.setErrors({ invalidAuth:true });
          },
        });
    }
    else {
     this.loginForm.markAsDirty();
    }
  }
}
