:host {
    flex: 1;
    display: flex;
    padding: 56px 0px 24px 81px;
    box-sizing: border-box;
    flex-direction: column;
    gap: 40px;
    overflow-y: auto;
}

.section-header,
.section-content {
    display: flex;
    max-width: 959px;
    margin: 0px auto;
}

.section-header {
    width: 100%;
    align-items: center;
    height: fit-content;
    gap: 16px;

    p {
        font-family: 'Visby CF';
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        color: #fff;
        line-height: 20px;
        margin: 0px;
    }
}

.section-content {
    width: 100%;
    flex-direction: column;
}

.common-icon-button {
    --mdc-icon-button-state-layer-size: 32px;
    width: var(--mdc-icon-button-state-layer-size);
    height: var(--mdc-icon-button-state-layer-size);
    padding: 4px;
    --mdc-icon-button-icon-color: white;
    background-color: rgba(255, 255, 255, 0.22);
    border-radius: 6px;  

    ::ng-deep .mat-mdc-button-touch-target {
        height:  var(--mdc-icon-button-state-layer-size);
        width: var(--mdc-icon-button-state-layer-size);
    }
    ::ng-deep .mat-mdc-button-persistent-ripple {
        border-radius: inherit;
    }
}