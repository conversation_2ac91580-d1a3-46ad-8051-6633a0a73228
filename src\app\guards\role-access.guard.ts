import { inject } from '@angular/core';
import { CanMatchFn } from '@angular/router';
import { UserProfileService } from '../services/user-profile-service';
import { map } from 'rxjs';

export const roleAccessGuard: CanMatchFn = (route, state) => {
  const userProfileService = inject(UserProfileService);
  return userProfileService.getProfile().pipe(
    map((res) => {
      if(res?.role === route.data?.['role']) {
        return true;
      }
      return false;
    })
  );
};
