<form class="utilites-container">
    <!--<button #searchDisplayButton (click)="openSearchDialog()" class="search-display-button">
        <mat-icon class="search-display-icon">search</mat-icon>
        <p class="search-display-button-text">Buscar artista, álbum, canción....</p>
        <mat-icon class="search-display-icon">expand_more</mat-icon>
    </button>-->
    <div class="date-utils-container">
        <div style="display: flex; align-self: flex-end; gap: 17px;">
            <div style="display: flex; flex-direction: column">
                <p class="custom-date-picker-header">Desde</p>
                <mat-form-field floatLabel="always" subscriptSizing="dynamic" appearance="outline"
                    class="custom-date-picker expand-datepicker">
                    <input matInput [matDatepicker]="datepicker"
                        [formControl]="reproductionsSearchForm.controls.initDate" readonly
                        [max]="reproductionsSearchForm.controls.limitDate.value !== null?reproductionsSearchForm.controls.limitDate.value:maxDate"
                        [disabled]="true" class="custom-date-picker-input" />
                    <mat-icon matPrefix class="custom-date-picker-icon">event</mat-icon>
                    <mat-datepicker-toggle matIconSuffix [for]="datepicker" class="custom-date-picker-toggle-icon">
                        <mat-icon matDatepickerToggleIcon class="custom-date-picker-toggle-icon">
                            keyboard_arrow_down
                        </mat-icon>
                    </mat-datepicker-toggle>
                    <mat-datepicker panelClass="menu-backdrop" #datepicker [calendarHeaderComponent]="customHeader">
                        <mat-datepicker-actions>
                            <button mat-button matDatepickerCancel style="border-radius: 12px">
                                Cancelar
                            </button>
                            <button mat-raised-button matDatepickerApply class="custom-date-picker-apply-btn">
                                Aceptar
                            </button>
                        </mat-datepicker-actions>
                    </mat-datepicker>
                </mat-form-field>
            </div>
            <div style="display: flex; flex-direction: column">
                <p class="custom-date-picker-header">Hasta</p>
                <mat-form-field floatLabel="always" subscriptSizing="dynamic" appearance="outline"
                    class="custom-date-picker expand-margin-datepicker">
                    <input matInput [matDatepicker]="datepickerv"
                        [formControl]="reproductionsSearchForm.controls.limitDate" readonly
                        [min]="reproductionsSearchForm.controls.initDate.value" [max]="maxDate"
                        placeholder=" -- / -- / -- " class="custom-date-picker-input expand-datepicker-input"
                        [style]="reproductionsSearchForm.controls.limitDate.value === null ? 'letter-spacing: 2.5px;':''" />
                    <mat-icon matPrefix class="custom-date-picker-icon">event</mat-icon>
                    <mat-datepicker-toggle matIconSuffix [for]="datepickerv" class="custom-date-picker-toggle-icon">
                        <mat-icon matDatepickerToggleIcon class="custom-date-picker-toggle-icon">keyboard_arrow_down
                        </mat-icon>
                    </mat-datepicker-toggle>
                    <mat-datepicker panelClass="menu-backdrop" #datepickerv disabled="false"
                        [calendarHeaderComponent]="customHeader">
                        <mat-datepicker-actions>
                            <button mat-button matDatepickerCancel>Cancelar</button>
                            <button mat-raised-button color="primary" matDatepickerApply
                                class="custom-date-picker-apply-btn">
                                Aceptar
                            </button>
                        </mat-datepicker-actions>
                    </mat-datepicker>
                </mat-form-field>
            </div>
        </div>
    </div>
</form>
<!--
<div class="key-overview-container">
    @if(keyOverviewConcepts){
        @for(concept of keyOverviewConcepts; track concept) {
            <div class="key-overview">
                <div class="key-overview-header">
                    <h4>{{concept.conceptKey}}</h4>
                    <mat-icon>{{concept.conceptIcon}}</mat-icon>
                </div>
                <div class="key-overview-content">
                    <h3>{{concept.conceptHeaderName}}</h3>
                    <h2>
                        {{qtyFormat(concept.conceptValue)}}
                    </h2>
                    <h6>Reproducciones</h6>
                </div>
            </div>  
        }
    }
</div>
-->
<div class="data-container">
    <div class="data-table-container">
        <mat-card style="background-color: #f7f9fb; border-radius: 12px">
            <table mat-table class="common-data-table" [dataSource]="table.dataSource" matMultiSort (matSortChange)="table.onSortEvent()"
                style="background-color: var(--table-background-color);border-radius: 12px;">
                <ng-container matColumnDef="artist">
                    <th mat-header-cell *matHeaderCellDef mat-multi-sort-header="artist">
                        Artista
                        <mat-icon *ngIf="isSortingActive('artist')" svgIcon="chevron_icon"
                            [class]="getSortingDirection('artist') === 'asc' ? 'custom-sort custom-sort-inverse':'custom-sort'"></mat-icon>
                    </th>
                    <td mat-cell *matCellDef="let row">{{ row.artist }}</td>
                </ng-container>
                <ng-container matColumnDef="song">
                    <th mat-header-cell *matHeaderCellDef mat-multi-sort-header="song">
                        Tema
                        <mat-icon *ngIf="isSortingActive('song')" svgIcon="chevron_icon"
                            [class]="getSortingDirection('song') === 'asc' ? 'custom-sort custom-sort-inverse':'custom-sort'"></mat-icon>
                    </th>
                    <td mat-cell *matCellDef="let row">{{ row.song }}</td>
                </ng-container>
                <ng-container matColumnDef="albumname">
                    <th mat-header-cell *matHeaderCellDef mat-multi-sort-header="album">
                        Álbum
                        <mat-icon *ngIf="isSortingActive('albumname')" svgIcon="chevron_icon"
                            [class]="getSortingDirection('albumname') === 'asc' ? 'custom-sort custom-sort-inverse':'custom-sort'"></mat-icon>
                    </th>
                    <td mat-cell *matCellDef="let row">
                        <div style="display: flex; align-items: center;justify-content: space-between;">
                            <span>
                                {{ row.albumname }}
                            </span>
                            <div>
                                <mat-icon svgIcon="music_record_icon" style="height: 16px;width: 16px;"
                                    (click)="invokeNavigateTo('dashboard/reproductions/album-detail/'+row.album_id)"></mat-icon>
                            </div>
                        </div>
                    </td>
                </ng-container>
                <ng-container matColumnDef="genre">
                    <th mat-header-cell *matHeaderCellDef mat-multi-sort-header="genre">
                        Género
                        <mat-icon *ngIf="isSortingActive('genre')" svgIcon="chevron_icon"
                            [class]="getSortingDirection('genre') === 'asc' ? 'custom-sort custom-sort-inverse':'custom-sort'"></mat-icon>
                    </th>
                    <td mat-cell *matCellDef="let row">{{ row.genre }}</td>
                </ng-container>
                <ng-container matColumnDef="recordLabel">
                    <th mat-header-cell *matHeaderCellDef mat-multi-sort-header="recordLabel">
                        Disquera
                        <mat-icon *ngIf="isSortingActive('recordlabel')" svgIcon="chevron_icon"
                            [class]="getSortingDirection('recordlabel') === 'asc' ? 'custom-sort custom-sort-inverse':'custom-sort'"></mat-icon>
                    </th>
                    <td mat-cell *matCellDef="let row">{{ row.recordlabel }}</td>
                </ng-container>
                <ng-container matColumnDef="reproductions">
                    <th mat-header-cell *matHeaderCellDef mat-multi-sort-header="reproductions">
                        Reproducciones
                        <mat-icon *ngIf="isSortingActive('reproductions')" svgIcon="chevron_icon"
                            [class]="getSortingDirection('reproductions') === 'asc' ? 'custom-sort custom-sort-inverse':'custom-sort'"></mat-icon>
                    </th>
                    <td mat-cell *matCellDef="let row" class="reproductions-column">
                        <div style="display: flex; align-items: center">
                            <mat-icon svgIcon="reproductions_icon" style="margin-right: 7.5px"></mat-icon>
                            <div style="display: flex;align-items: center;width: 65px;justify-content: space-between;">
                                <span>
                                    {{ quantityParser(row.reproductions) }}
                                </span>
                                <mat-icon [svgIcon]="row.reproductions > row.previous_reproductions ? 'up_tendency_icon': 'down_tendency_icon'"
                                    style="width: 16px;height: 16px;"></mat-icon>
                            </div>
                        </div>
                    </td>
                </ng-container>
                <tr mat-header-row *matHeaderRowDef="table.displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: table.displayedColumns;"></tr>
            </table>
            <mat-paginator [pageSizeOptions]="pageSizes" aria-label="Select page of songs"
                itemsPerPageLabel="Filas por página"
                style="background-color: var(--table-background-color);border-top: 1px solid #a8c5da99;"
                [pageSize]="table.pageSize" [pageIndex]="table.pageIndex"
                [length]="table.totalElements ? table.totalElements : 0" (page)="onPageChange($event)">
            </mat-paginator>
        </mat-card>
    </div>
    <div class="data-actions-container">
        <button mat-fab color="accent" style="margin-bottom: -28px;" (click)="exportData()">
            <mat-icon svgIcon="download_icon"></mat-icon>
        </button>
    </div>
</div>