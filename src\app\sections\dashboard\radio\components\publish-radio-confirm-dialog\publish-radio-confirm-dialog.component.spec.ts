import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PublishRadioConfirmDialogComponent } from './publish-radio-confirm-dialog.component';

describe('PublishRadioConfirmDialogComponent', () => {
  let component: PublishRadioConfirmDialogComponent;
  let fixture: ComponentFixture<PublishRadioConfirmDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PublishRadioConfirmDialogComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(PublishRadioConfirmDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
