<div class="app">
  <p class="section-title">{{ asText }}</p>
  <div class="top-section">
    <form [formGroup]="playlistForm" (ngSubmit)="onSubmit($event)" class="inputs" novalidate>
      <div class="form-content">
        <!-- Zona de carga de imagen -->
        <div class="image-upload-container">
        <label for="imageUpload">
          <div class="icon" 
            (click)="activateIcon()" 
            (drop)="onDrop($event); activateIcon()" 
            (dragover)="onDragOver($event)" 
            (dragleave)="onDragLeave($event)"
          >
            <!-- Input para cargar la imagen -->
            <input type="file" 
                id="imageUpload" 
                name="imageUpload"
                [src]="playlistImage" 
                (change)="handleFileInput($event)" 
                style="display:none;"
                *ngIf="showInputs"
            />
            
            <!-- Si el modo es 'Crear playlist', muestra el ícono o la imagen cargada -->
            <mat-icon *ngIf="modo === 'Crear playlist' && !playlistImage" [svgIcon]="icono" style="width: 126px;height: 126px;"></mat-icon>
             <!-- Si el modo es 'Crear playlist', muestra la imagen si existe -->
            <img *ngIf="modo === 'Crear playlist' && playlistImage" [src]="playlistImage" alt="Imagen de la playlist" style="width: 126px;height: 126px;">
            
            <!-- Si el modo es 'Actualizar playlist', muestra la imagen -->
            <img *ngIf="modo === 'Actualizar playlist'" [src]="playlistImage" alt="Imagen de la playlist" style="width: 126px;height: 126px;">
            
            <!-- Si el modo es 'Crear playlist', muestra 'Agregar imagen' -->
            <p *ngIf="mostrarTextoIcono && modo === 'Crear playlist' && showInputs" class="texto_imagen">Agregar imagen</p>
            
            <!-- Si el modo es 'Actualizar playlist', muestra 'Editar imagen' -->
            <p *ngIf="mostrarTextoIcono && modo === 'Actualizar playlist' && showInputs" class="texto_imagen">Editar imagen</p>
          </div>
        </label>
        <div *ngIf="!imageLoaded && formSubmitted" class="error">
          Debe cargar una imagen para la playlist.
        </div>
        </div>
        
        <!-- Otros campos del formulario -->
        <div class="other-fields">
          <!-- Título del playlist -->
          <div class="input-field">
            <label for="playlist-title" class="input-titulo">Titulo del playlist</label>
            <input formControlName="playlistTitle" 
                type="text" id="playlist-title" 
                name="playlist-title" 
                [value]="playlistTitleValue"
                placeholder="Ingresa nombre del playlist" 
                class="input-titulo"
                maxlength="40" 
                (keydown.enter)="$event.preventDefault()" 
                (focus)="activateIcon()" required
                *ngIf="showInputs"
              >
            <div *ngIf="playlistForm.controls['playlistTitle'].invalid && formSubmitted" class="error">
              El campo debe contener título del playlist
            </div>
          </div>
          <!-- Valor del título del playlist -->
          <div class="valor-procesado" *ngIf="!showInputs">
            {{ playlistForm.get('playlistTitle')?.value }}
          </div>
          
          <!-- Descripción -->
          <div class="input-field-textarea">
            <label for="description" class="input-titulo">Descripción</label>
              <textarea formControlName="description" 
                id="description" 
                name="description" 
                [value]="descriptionValue"
                placeholder="Escribe algo..." 
                class="input-titulo" 
                (keydown.enter)="$event.preventDefault()" 
                (focus)="activateIcon()" required
                *ngIf="showInputs"
              >
                
              </textarea>
            <div *ngIf="playlistForm.controls['description'].invalid && formSubmitted" class="error">
              El campo debe contener información description
            </div>
          </div>
          <!-- Valor del description del playlist -->
          <div class="valor-procesado" *ngIf="!showInputs">
            {{ playlistForm.get('description')?.value }}
          </div>    
          
          <!-- Canciones -->
          <div class="input-field" style="width: 96%">
            <label for="songs" class="input-titulo">Canciones</label>
            <div style="display: flex; align-items: center; position: relative;">
              <mat-icon svgIcon="lupa_icon" style="position: absolute; left: 10px; cursor: pointer;" (click)="buscarCanciones(playlistForm.get('songs')?.value)" *ngIf="showInputs"></mat-icon>
              <input formControlName="songs" 
                  type="text" 
                  id="songs" 
                  name="songs" 
                  placeholder="Ingresa el nombre de la canción" 
                  class="input-titulo" 
                  (keydown.enter)="$event.preventDefault()" 
                  (focus)="activateIcon()" 
                  required style="padding-left: 44px;" 
                  autocomplete="off" 
                  [matAutocomplete]="auto"
                  *ngIf="showInputs"
                  (ngModelChange)="buscarCanciones(playlistForm.get('songs')?.value)"
                >
              <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayFn">
                <mat-option *ngFor="let cancion of canciones" [value]="cancion" (click)="addChip(cancion)">
                  {{cancion.nombre}}
                </mat-option>
              </mat-autocomplete>
            </div>
            <div *ngIf="selectedSongs.length === 0 && formSubmitted" class="error">
              Debe contener canciones seleccionadas, para guardarlas dentro del playlist
            </div>                
          </div>

          <!-- Lista de canciones -->
          <mat-chip-listbox class="chip-list">
            <mat-chip-option *ngFor="let song of selectedSongs" [value]="song.id">
              <span style="color: aliceblue; text-align: center; width: 222px; white-space: normal; word-wrap: break-word;">
                <!-- Si el modo es 'Crear playlist', muestra {{song.nombre}} - {{song.artista}} -->
                <ng-container *ngIf="modo === 'Crear playlist'">{{song.nombre.slice(0, 22)}} - {{song.artista.slice(0, 12)}}</ng-container>
                
                <!-- Si el modo es 'Actualizar playlist', muestra {{song.nombre}} - {{song.artistaname}} -->
                <ng-container *ngIf="modo === 'Actualizar playlist'">{{song.nombre.slice(0, 22)}} - {{song.artista ? song.artista.slice(0, 12) : (song.artistaname ? song.artistaname.slice(0, 12) : '')}}</ng-container>
              </span>
              <button matChipRemove (click)="removeChip(song)" style="width: var(--24, 24px);height: var(--24, 24px);padding-left: 0px;padding-right: 0px;">
                <mat-icon [svgIcon]="'close-cancion_icon'" ></mat-icon>
              </button>
            </mat-chip-option>                    
          </mat-chip-listbox>

          <div class="image_cancion" *ngIf="selectedSongs.length === 0">
            <mat-icon svgIcon="list_alt_add_cancion_icon"></mat-icon>
          </div>
          
          <!-- Mensaje -->
          <div class="message" *ngIf="selectedSongs.length === 0">
            Aún no has agregado la primera canción, usa el buscador para encontrarla.
          </div>

          <!-- Botón de envío -->
          <div class="area-button-submit">
            <div *ngIf="!showInputs" style="margin-right: 24px;margin-top: 5px;">
              <a href="{{urlplaylist}}" class="url-playlist alinear-icono-texto">
                <span><mat-icon  [svgIcon]="iconoUrlPlaylist" style="width: 26px;height: 26px;margin-right: 10px;margin-top: 4px;"></mat-icon></span> 
                <span>Copiar URL del playlist</span>
              </a>
            </div>
            <div *ngIf="!showInputs" style="margin-right: 24px;">
              <!-- Botón 'Ver en Acustico' -->
              <a href="https://aqustico.com/" target="_blank">
                <button type="button" class="btn-acustico">
                  Ver en Acustico
                </button>
              </a>
            </div>
            <button type="submit" 
              [style.opacity]="((playlistForm.valid && selectedSongs.length > 0 && imageLoaded) || activatebutton) ? '1' : '0.5'" 
              [style.cursor]="((playlistForm.valid && selectedSongs.length > 0 && imageLoaded) || activatebutton) ? 'pointer' : 'not-allowed'" 
              (click)="formSubmitted = true">{{ showInputs ? modo : 'Editar Playlist' }}
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
