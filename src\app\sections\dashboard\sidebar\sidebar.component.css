:host {
    height: 100%;
    width: 100%;
    border-right: 1px solid #ffffff40;
    padding: 16px 16px 0px 16px;
}

.sidebar-logo {
    margin-bottom: 14px;
    padding: 4px;
    background-position: center;
}

.sidebar-section-text {
    color: var(--AquaLight, #6bdccc);
    font-feature-settings: "cv11" on, "cv01" on, "ss01" on;
    font-family: "Visby CF";
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px;
    margin: 0px;
    padding: 4px 0px 4px 12px;
}

app-footer-content {
    margin-bottom: 8.5px;
}

@media screen and (min-height: 1020px) {
    app-footer-content {
        margin-bottom: 5px;
    }
}

.sidebar-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: start;
}

.sidebar-section-container {
    display: flex;
    flex-direction: column;
    padding: 8px 0px 20px 0px;
}

.sidebar-item {
    display: flex;
    align-items: center;
    padding: 8px 8px 8px 8px;
    cursor: pointer;
    color: white;
    font-weight: 500;
}

.sidebar-section-item-text {
    font-family: "Visby CF";
    font-size: 14px;
    font-weight: inherit;
    line-height: 20px;
    letter-spacing: 0em;
    margin-bottom: 0;
    margin-left: 8px;
    text-align: left;
    transition: margin-left 500ms ease-in-out;
}

.sidebar-section-item-text:hover {
    margin-left: 16px;
    transition: margin-left 500ms ease-in-out;
}

.sidebar-container ::ng-deep .mat-icon {
    width: 24px;
    height: 24px;
}

.sidebar-section-item-selected {
    color: #FFD53D;
    font-weight: 600;
}

.sidebar-section-item-cancion-selected {
    color: #FFD53D;
}

.sidebar-section-item-playlist-selected {
    color: #FFD53D;
}


.clase-icono-playlist {
    color: #FFD53D;
}