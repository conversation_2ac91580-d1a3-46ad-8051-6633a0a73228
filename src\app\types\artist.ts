import { SearchReproductionsSimple } from "./reproductions";

export interface ArtistType {
    estatus: string;
    fechacreacion: string;
    id: number;
    nombre: string;
}
export interface ArtistReproductions { 
  items: any[], meta: { currentPage: number, itemCount: number, itemsPerPage: number, totalItems: number, totalPages: number } 
}

export interface ArtistSearchParams extends Omit<SearchReproductionsSimple, "carrier" | "albumId"> {}