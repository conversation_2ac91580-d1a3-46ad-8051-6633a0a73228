import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, of  } from 'rxjs';

@Injectable({
    providedIn: 'root',
  })
export class RoutingService {
  constructor(private router:Router){}
  add(route: string) {
    this.router.navigate([route]);
  }
  getNewRoute() : Observable<string> {
    return of(this.router.url);
  }
  getCurrentRoute() : string {
    return this.router.url;
  }
}