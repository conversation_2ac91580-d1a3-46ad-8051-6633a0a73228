
<!--<div style="width: 90%; margin: auto;">
  <mat-form-field style="width: 100%;">
    <mat-label>Filter</mat-label>
    <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Mia" #input>
  </mat-form-field>
</div>-->
  
<div class="mat-elevation-z8" style="width: 90%; margin: auto">
  <div style="border-radius: 10px; overflow: hidden;">
    <table mat-table [dataSource]="dataSource" matSort>
  
      <!-- ID Column -->
      <ng-container matColumnDef="playlist_id">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> ID </th>
        <td mat-cell *matCellDef="let row"> {{row.playlist_id}} </td>
      </ng-container>
  
      <!-- Name Column -->
      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Título </th>
        <td mat-cell *matCellDef="let row"> {{row.name}}</td>
      </ng-container>
  
      <!-- Description Column -->
      <ng-container matColumnDef="descripcion">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Descripción </th>
        <td mat-cell *matCellDef="let row"> {{row.descripcion}} </td>
      </ng-container>
  
      <!-- Buttons Column -->
      <ng-container matColumnDef="botones">
        <th mat-header-cell *matHeaderCellDef>  </th>
        <td mat-cell *matCellDef="let row"> <button class="boton-editar" (click)="onEditarClick(row.playlist_id)">Editar</button>
        </td>
      </ng-container>
  
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
  
      <!-- Row shown when there is no matching data. -->
      <tr class="mat-row" *matNoDataRow>
        <!--<td class="mat-cell" colspan="4">No data matching the filter "{{input.value}}"</td>-->
      </tr>
    </table>

    
    <mat-paginator [pageSize]="5"  aria-label="Select page of users" showFirstLastButtons></mat-paginator>

  </div>
</div>

  
  
