import { Component, Input, OnInit, ViewChild, ElementRef, ChangeDetectorRef, AfterViewInit  } from '@angular/core';
import { FormControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Observable } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { BusquedaService } from '../../../../services/busqueda-service';
import { MatAutocompleteTrigger } from '@angular/material/autocomplete';
import { BuscarplaylistService } from '../../../../services/buscarplaylist-service';
import { CreatePlaylistService } from '../../../../services/crearplaylist-service';
import { UpdatePlaylistService } from '../../../../services/actualizarplaylist-service';
import { environment } from '../../../../../environments/environment';
import { ActivatedRoute } from '@angular/router';
import { CustomSnackbarComponent } from '../../../../components/custom-snackbar/custom-snackbar.component';
import { MatSnackBar } from '@angular/material/snack-bar';


@Component({
  selector: 'app-actualizar-playlist',
  templateUrl: './actualizar-playlist.component.html',
  styleUrls: ['./actualizar-playlist.component.css']
})
export class ActualizarPlaylistComponent implements OnInit {
  @Input() modoCrearPlaylist: boolean = false;
  @Input() playlistId: string = '';

  playlistForm: FormGroup = this.fb.group({});

  modo: string = '';
  formSubmitted = false;

  filteredSongs: any[] = [];

  selectedSongs: any[] = [];

  asText = '';

  songControl = new FormControl();
  

  mostrarTextoIcono: boolean = false; // Nueva variable

  icono = 'notamusical_icon'; // Variable para el nombre del icono

  playlistTitleValue = '';
  descriptionValue = '';
  songsValue = '';
  playlistImage: string = '';
  imageLoaded: boolean = false;
  activatebutton: boolean = false;
  showInputs: boolean = true;
  iniciar: boolean = false;
  iconoUrlPlaylist = 'link-playlist_icon';
  urlplaylist = `${environment.acusticoUrlPortal}/playlist/`;
  durationInSeconds = 10;


  // Continuación del arreglo...
   canciones: any[] = [];

  
   constructor(
    private fb: FormBuilder,
    private busquedaService: BusquedaService, 
    private buscarplaylistService: BuscarplaylistService,
    private crearPlaylistService: CreatePlaylistService,
    private updatePlaylistService: UpdatePlaylistService,
    private cd: ChangeDetectorRef,
    private route: ActivatedRoute,
    private _snackBar: MatSnackBar,
  ) {
    if(this.route.snapshot.params["playlist_id"]) {
      this.playlistId = this.route.snapshot.params["playlist_id"];
      this.asText = 'Editar playlist';
    }else {
      this.asText = 'Nueva playlist';
      this.modoCrearPlaylist = true; 
    }
   }

  @ViewChild('playlistTitle') playlistTitleInput!: ElementRef;
  @ViewChild('description') descriptionInput!: ElementRef;
  @ViewChild('songs') songsInput!: ElementRef;
  @ViewChild(MatAutocompleteTrigger) autocompleteTrigger!: MatAutocompleteTrigger;


  ngOnInit() {
  
    if (this.modoCrearPlaylist) {
      this.modo = 'Crear playlist';
    } else {
      this.modo = 'Actualizar playlist';
      this.getPlaylist();
      this.imageLoaded = true;
    }
    if (this.modo === 'Actualizar playlist') {
      this.getPlaylist();
    }

    this.playlistForm = this.fb.group({
      playlistTitle: ['', Validators.required],
      description: ['', Validators.required],
      songs: ['', Validators.required], 
      // ...
    });

    this.playlistForm.valueChanges.subscribe((formValues) => {
        this.activatebutton = true;
    });
   
  }



  snack_mensaje(msg: string): void {
    this._snackBar.openFromComponent(CustomSnackbarComponent, {
      duration: this.durationInSeconds * 1000,
      horizontalPosition: "center",
      verticalPosition: "top",
      data: { message: msg },
      panelClass: ['error-snackbar'],
    });
  }
  
  getFileNameFromUrl(url: string): string {
    const urlParts = url.split('/');
    return urlParts[urlParts.length - 1];
  }
  
  

  onSubmit(event: Event) {
    event.preventDefault();

    // Si el botón dice 'Editar Playlist', cambia al modo de edición
    if (!this.showInputs) {
      this.modo = 'Actualizar playlist';
      this.playlistId = this.playlistId; 
      this.showInputs = true;
      return;
    }
    
    this.formSubmitted = true;

    // Validación de la imagen
      let fileInput = document.getElementById('imageUpload') as HTMLInputElement;
      let file: File = fileInput.files ? fileInput.files[0] : new File([], '');

      //let fileNameimg = '';
      
      if (!file || file.size === 0) {   
          if (this.modo === 'Actualizar playlist') { 
            //this.imageLoaded = false;
            //return;
          }else{
            this.imageLoaded = false;
            return;
          }
      }

      
    if (this.playlistForm.valid && this.selectedSongs.length > 0) {
      let fileInput = document.getElementById('imageUpload') as HTMLInputElement; 
      console.log('fileInput salvar', fileInput.files);
      
      let file: File = fileInput.files ? fileInput.files[0] : new File([], '');  
  
      let playlist = {
        name: this.playlistForm.get('playlistTitle')?.value,
        descripcion: this.playlistForm.get('description')?.value,
        songs: this.selectedSongs.map(song => song.id)
      };

      
  
      if (this.modo === 'Actualizar playlist') {
        const id = Number(this.playlistId);
        this.updatePlaylistService.updatePlaylist(id, playlist, file).subscribe({
          next: (response) => {
            this.showInputs = false;
            this.urlplaylist = this.urlplaylist + id.toString();
            if(response.body?.httpCode === 200){
              console.log('Playlist actualizada:', response.body?.systemMessage);
            }else{
              console.error('Error actualizando la playlist:', response.body?.systemMessage);
              if (response.body && response.body.systemMessage) {
                this.snack_mensaje(response.body.systemMessage);
              } else {
                this.snack_mensaje('Error actualizando la playlist desconocido.');
              }
            }       
          },
          error: (error) => {
            console.error('Error actualizando la playlist:', error);
            if (error) {
              this.snack_mensaje(error);
            } else {
              this.snack_mensaje('Error actualizando la playlist desconocido.');
            }
          }
        });

        
        
      } else {
        this.crearPlaylistService.createPlaylist(playlist, file).subscribe({
          next: (response) => {
            this.showInputs = false;
            console.log('Playlist creada:', response.body);
            if (response.body && response.body.playlist && response.body.playlist.playlist_id && response.body?.httpCode === 200) {
              this.playlistId = response.body.playlist.playlist_id.toString(); // Actualiza playlistId con el ID de la playlist recién creada
              this.urlplaylist = this.urlplaylist + this.playlistId;
            }else{
              console.error('Error actualizando la playlist:', response.body?.systemMessage);
              if (response.body && response.body.systemMessage) {
                this.snack_mensaje(response.body.systemMessage);
              } else {
                this.snack_mensaje('Error desconocido actualizando la playlist.');
              }
            }
          },
          error: (error) => {
            console.error('Error creando la playlist:', error);
            if (error) {
              this.snack_mensaje(error);
            } else {
              this.snack_mensaje('Error desconocido creando la playlist.');
            }
          }
        });
        
      }
    }
  }
  

  onInputChange() {
    this.icono = 'editplaylist_icon';
    this.mostrarTextoIcono = true;
  
    // Convierte el valor de entrada a minúsculas
    const inputValue = this.songsValue.toLowerCase();
  
    // Filtra las canciones basado en lo que el usuario ha ingresado
    this.filteredSongs = this.canciones.filter(cancion => cancion.nombre.toLowerCase().includes(inputValue));
  }
  

  displayFn(cancion: any): string {
    return cancion && cancion.nombre ? cancion.nombre : '';
  }
  
  addChip(cancion: any): void {
    // Verifica si la canción ya ha sido seleccionada
    if (!this.selectedSongs.find(song => song.id === cancion.id)) {
      // Asegúrate de que cancion.id es un número, no un array
      let id = Array.isArray(cancion.id) ? cancion.id[0] : cancion.id;
      this.selectedSongs.push({ ...cancion, id: Number(id) });
    }
  }
  
  removeChip(cancion: any): void {
    const index = this.selectedSongs.indexOf(cancion);
  
    if (index >= 0) {
      this.selectedSongs.splice(index, 1);
    }
  }


  // Método obtener canciones
  buscarCanciones(cadena: string): void {
    this.busquedaService.buscar(cadena).subscribe({
      next: (response) => {
        if (response.body) {
          // Mapea los datos de las canciones a la estructura deseada
          this.canciones = response.body.songs.map(cancion => ({
            id: cancion.id,
            nombre: cancion.nombre,
            artista: cancion.idalbum && cancion.idalbum.idartista ? cancion.idalbum.idartista.nombre : ''
          }));
    
          this.autocompleteTrigger.openPanel();
        } else {
          console.error('La respuesta del servidor no contiene ningún cuerpo');
        }
      },
      error: (error) => {
        console.error(error);
      }
    });
    
  }
  
  // Método obtenerPlaylist
  getPlaylist() {
    const id = Number(this.playlistId);
  
    this.buscarplaylistService.obtenerPlaylist(id).subscribe({
      next: (response) => {
        if (this.modo === 'Actualizar playlist' && response.body) {
          this.playlistTitleValue = response.body.playlist.name;
          this.descriptionValue = response.body.playlist.descripcion;
          this.playlistImage = response.body.playlist.image;
          this.selectedSongs = response.body.playlist.songs;

  
          // Actualiza los valores de los campos del formulario
          this.playlistForm.controls['playlistTitle'].setValue(this.playlistTitleValue);
          this.playlistForm.controls['description'].setValue(this.descriptionValue);
          this.playlistForm.controls['songs'].setValue(this.selectedSongs);
  
          // Marca los campos del formulario como tocados y actualiza su validez
          this.playlistForm.controls['playlistTitle'].markAsTouched();
          this.playlistForm.controls['playlistTitle'].updateValueAndValidity();
          this.playlistForm.controls['description'].markAsTouched();
          this.playlistForm.controls['description'].updateValueAndValidity();
          this.playlistForm.controls['songs'].markAsTouched();
          this.playlistForm.controls['songs'].updateValueAndValidity();
  
          this.imageLoaded = false;
          this.cd.markForCheck();
        }
      },
      error: (error) => {
        console.error('Error obteniendo la playlist:', error);
      }
    });
  }
  

    handleFileInput(event: Event) {
      let target = event.target as HTMLInputElement;
      let files = target.files;
      if (files) {
        this.activatebutton = true;
        this.processFiles(files);
      }
    }

    processFiles(files: FileList) {
      let file = files.item(0);
      if (file) {
        let reader = new FileReader();
        reader.onload = (event: any) => {
          this.playlistImage = event.target.result;
          this.imageLoaded = true; 
        }
        reader.readAsDataURL(file);
      }
    }
    
    
    onDragOver(event: DragEvent) {
      event.preventDefault();
      // Puedes agregar lógica aquí para cambiar el estilo del div cuando se arrastra un archivo sobre él
    }
    
    onDragLeave(event: DragEvent) {
      event.preventDefault();
      // Puedes agregar lógica aquí para revertir los cambios de estilo cuando el archivo deja de arrastrarse sobre el div
    }
    
    onDrop(event: DragEvent) {
      event.preventDefault();
      let files = event.dataTransfer?.files;
      if (files) {
        this.processFiles(files);
      }
    }
    
    // Método para activar el ícono y el texto
    activateIcon(): void {
        this.icono = 'editplaylist_icon';
        this.mostrarTextoIcono = true;
    }

}

