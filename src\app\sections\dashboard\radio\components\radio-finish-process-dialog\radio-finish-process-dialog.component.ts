import { Component, Inject } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { RoutingService } from "../../../../../services/routing-service";
import { RadioService } from "../../../../../services/radio-service";

@Component({
  selector: "app-radio-finish-process-dialog",
  templateUrl: "./radio-finish-process-dialog.component.html",
  styleUrl: "./radio-finish-process-dialog.component.css",
})
export class RadioFinishProcessDialogComponent {
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<RadioFinishProcessDialogComponent>,
    private readonly routingService: RoutingService,
    private readonly radioService: RadioService
  ) {}

  onStopConfirm () {
    const {VALID_FORM,...radioData} = this.data; 
    if (!VALID_FORM) {
      this.routingService.add('/dashboard/radio');
      this.onBack();
    }
    else {
      this.radioService.createRadio(radioData).subscribe({
        next: (response) => {
          this.routingService.add('/dashboard/radio');
          this.onBack();
        },
      });
    }
  }

  onBack() {
    this.dialogRef.close();
  }

  onStopCancel () {   
    this.routingService.add('/dashboard/radio');
    this.onBack();
  }
}
