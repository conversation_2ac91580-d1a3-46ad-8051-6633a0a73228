import {SearchReproductionsSimple, SongPlayback } from "./reproductions";

export enum ConceptKey {
  ARTIST = "Artista más escuchado",
  ALBUM = "Álbum más escuchado",
  SONG = "Canción más escuchado",
}
export interface KeyOverviewConcept {
  conceptKey: ConceptKey;
  conceptHeaderName: string;
  conceptValue: number;
  conceptIcon: string;
}

export interface RecordLabelSearchParams extends Omit<SearchReproductionsSimple, "carrier" | "albumId"> {}

export interface RecordLabelReproductions { 
  items: SongPlayback[], meta: { currentPage: number, itemCount: number, itemsPerPage: number, totalItems: number, totalPages: number } 
}

export interface RecordLabelType {
  active: boolean;
  createdAt: string;
  id: number;
  name: string;
  updatedAt: string;
}
