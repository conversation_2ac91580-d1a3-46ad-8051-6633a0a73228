<style>
  :host {
    align-items: center;
    display: flex;
    flex-direction: column;
    background-image: radial-gradient(
        circle 45.6vw at 3.75% 112.5%,
        #550dc5,
        transparent
      ),
      url("assets/aqustico_login_background.jpeg");
    background-size: cover;
    height: fit-content;
    height: 100%;
    width: 100%;
  }

  .site-title {
    margin-top: 70.75px;
  }

  .form-field {
    width: 100%;
  }

  .login-layout {
    width: 413px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .login-footer {
    justify-content: flex-end;
    margin-bottom: -8px;
  }

  .section-title {
    color: #fff;
    font-feature-settings: "clig" off, "liga" off;
    font-family: "Visby CF";
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 28px;
    letter-spacing: 0.2px;
    margin-bottom: 10px;
  }

  .footer-text {
    color: rgba(255, 255, 255, 0.6);
    text-align: center;
    font-family: "Visby CF";
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px;
    letter-spacing: 0.72px;
  }

  .footer-logo {
    background-image: url("assets/conectium_logo.png");
    background-repeat: no-repeat;
    height: 25px;
    width: 87px;
  }

  .login-header {
    margin-top: 87px;
    margin-bottom: 71px;
    width: 100%;
    justify-content: center;
    display: flex;
  }

  .login-form {
    flex: 1;
    flex-direction: column;
    display: flex;
  }

  .login-form-submit {
    color: #000;
    text-align: center;
    font-family: "Visby CF";
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    width: 100%;
    border-radius: 12px;
    background: #00e5ef;
    filter: drop-shadow(0px 6px 8px rgba(0, 0, 0, 0.18));
    padding: 12px 0px 13px 0px;
    border-style: none;
    margin-top: 25px;
  }

  .login-form-field-header {
    color: #fff;
    font-feature-settings: "clig" off, "liga" off;
    font-family: "Visby CF";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    margin-top: 23px;
  }

  .login-form-header {
    color: #fff;
    font-feature-settings: "clig" off, "liga" off;
    font-family: "Visby CF";
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 28px;
    letter-spacing: 0.2px;
    margin-top: 25px;
    margin-bottom: 0px;
  }

  .login-layout-container {
    height: 100%;
    width: 100%;
    justify-content: center;
    align-items: center;
    display: flex;
  }

  .outlined-input {
    --mdc-outlined-text-field-input-text-color: white;
    --mat-form-field-container-text-size: 14px;
    --mat-form-field-container-text-line-height: 22px;
    --mat-form-field-container-height: 22px;
    --mat-form-field-container-text-weight: 500;
    --mat-form-field-container-vertical-padding: 14px;
    --as-border-color: transparent;
    --as-border-color-error: #ff003f;
    --mdc-outlined-text-field-hover-outline-color: var(--as-border-color);
    --mdc-outlined-text-field-outline-color: var(--as-border-color);
    --mdc-outlined-text-field-focus-outline-color: var(--as-border-color);

    --mdc-outlined-text-field-caret-color: white;
    --mdc-outlined-text-field-error-caret-color: white;

    border-radius: 12px;
    background-color: #ffffff1f;
    --mdc-outlined-text-field-container-shape: 12px;
    --mdc-outlined-text-field-input-text-placeholder-color: white;

    --mdc-outlined-text-field-error-outline-color: var(--as-border-color-error);

    --mdc-outlined-text-field-error-focus-outline-color: var(
      --as-border-color-error
    );
    
    --mdc-outlined-text-field-error-hover-outline-color: var(
      --as-border-color-error
    );

    --mdc-outlined-text-field-outline-width: 2px;

    --mat-form-field-container-text-tracking: 0.14px;

    font-family: Visby CF;
    color: white;
  }

  .custom-helper {
    background-color: white;
    font-family: "Noto Sans";
    font-size: 10px;
    font-weight: 600;
    line-height: 14px;
    letter-spacing: 0em;
    text-align: left;
    height: 28px;
    display: flex;
    align-items: center;
    border-radius: 8px;
    margin-top: 8px;
    padding: 4px 8px;
    color: #f86060;
  }

  .custom-helper > p {
    margin: 0px;
  }

  .custom-helper > mat-icon {
    margin-right: 5px;
    font-size: 16px;
    width: 16px;
    height: 16px;
  }

  .password-input {
    font-family: small-caption;
  }

  @media screen and (max-height: 720px) {
    .login-header {
      margin-bottom: 10px;
      margin-top: 10px;
    }
    .login-form {
      margin: 10px 0px 10px 0px;
    }
  }
</style>
<div class="login-layout">
  <div class="login-header">
    <mat-icon
      svgIcon="aqustico_logo"
      style="width: 290px; height: 100%"
    ></mat-icon>
  </div>
  <div class="login-form">
    <p class="section-title">Dashboard Aqustico</p>
    <mat-divider
      style="
        width: 100%;
        border-top-width: 1px;
        border-top-color: rgba(255, 255, 255, 0.25);
        margin-top: -5px;
      "
    ></mat-divider>
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
      <div>
        <p class="login-form-header">Iniciar Sesión</p>
      </div>
      <div>
        <h6 class="login-form-field-header">Usuario</h6>
      </div>
      <mat-form-field
        subscriptSizing="dynamic"
        appearance="outline"
        class="outlined-input"
      >
        <input matInput [formControl]="loginForm.controls.email" type="text"/>
      </mat-form-field>
      @if ((loginForm.pristine === false && loginForm.controls.email.errors !== null) || loginForm.errors !== null) {
      <div class="custom-helper">
        <mat-icon svgIcon="alert_icon"></mat-icon>
        <p>Correo inválido. Intenta nuevamente.</p>
      </div>
      }
      <div>
        <h6 class="login-form-field-header">Contraseña</h6>
      </div>
      <mat-form-field
        subscriptSizing="dynamic"
        appearance="outline"
        class="outlined-input password-input"
      >
        <input
          matInput
          [formControl]="loginForm.controls.password"
          [attr.type]="showPassword === true ? 'text' : 'password'"
          required
        />
      </mat-form-field>
      @if ((loginForm.pristine === false && loginForm.controls.password.errors !== null) || loginForm.errors !== null) {
      <div class="custom-helper">
        <mat-icon svgIcon="alert_icon"></mat-icon>
        <p>Contraseña inválida. Intenta nuevamente.</p>
      </div>
      }
      <button type="submit" class="form-field login-form-submit">
        Iniciar sesión
      </button>
    </form>
  </div>
  <app-footer-content class="login-footer"></app-footer-content>
</div>
