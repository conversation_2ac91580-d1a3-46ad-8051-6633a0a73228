{"name": "dashboard-aqustico-frontend", "version": "0.10.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build-qa": "ng build --base-href /dashboardqa-aqustico/ --configuration qa", "build-prod": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "17.2.1", "@angular/cdk": "17.2.0", "@angular/common": "17.2.1", "@angular/compiler": "17.2.1", "@angular/core": "17.2.1", "@angular/forms": "17.2.1", "@angular/material": "17.2.0", "@angular/material-moment-adapter": "17.2.0", "@angular/platform-browser": "17.2.1", "@angular/platform-browser-dynamic": "17.2.1", "@angular/router": "17.2.1", "@angular/service-worker": "17.2.1", "@auth0/angular-jwt": "5.2.0", "@types/moment-duration-format": "2.2.6", "chart.js": "4.4.2", "export-to-csv": "1.2.4", "file-saver": "^2.0.5", "js-abbreviation-number": "1.4.0", "moment": "2.30.1", "moment-duration-format": "2.3.2", "ngx-cookie-service": "17.1.0", "ngx-mat-multi-sort": "17.1.0", "rxjs": "7.8.1", "tslib": "2.6.2", "zone.js": "0.14.4"}, "devDependencies": {"@angular-devkit/build-angular": "17.2.0", "@angular/cli": "^17.3.4", "@angular/compiler-cli": "17.2.1", "@types/file-saver": "^2.0.7", "@types/chart.js": "2.9.41", "@types/jasmine": "5.1.4", "jasmine-core": "5.1.2", "karma": "6.4.2", "karma-chrome-launcher": "3.2.0", "karma-coverage": "2.2.1", "karma-jasmine": "5.1.0", "karma-jasmine-html-reporter": "2.1.0", "typescript": "5.3.3"}}