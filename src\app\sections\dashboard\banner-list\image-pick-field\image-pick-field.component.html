<div class="image-pick-field" (click)="filePicker.click()">
    <div class="image-pick-preview" (mouseover)="mouseEnter()" (mouseout)="mouseLeave()">
       <mat-icon *ngIf="asRadioCover === null" [style.opacity]="isRadioCoverFieldHovered ? 0:1" class="material-symbols-outlined">
        imagesmode
       </mat-icon>
       <mat-icon *ngIf="asRadioCover === null" [style.opacity]="isRadioCoverFieldHovered ? 1:0" class="material-symbols-outlined">
        upload_2
       </mat-icon>
        <img *ngIf="asRadioCover !== null" [src]="asRadioCover" alt="Radio Cover"/> 
    </div>
    <h5>{{asFieldName}}</h5>
    <h6>Medidas {{asDimensionSpecs.width*2 + 'x' + asDimensionSpecs.height*2 + 'px'}}</h6>
    <input type="file" #filePicker (change)="validateImageDimensions($event)" hidden>
</div>