<mat-grid-list cols="1" rowHeight="10%">
  <mat-grid-tile [colspan]="1" [rowspan]="8">
    <div class="sidebar-container">
      <img src="assets/sidebar_logo.png" class="sidebar-logo" />
        <div class="sidebar-section-container" *ngIf="sectionAccess['dashboards'] === true">
          <h5 class="sidebar-section-text">Dashboards</h5>
          @for (section of availableSections; track section) {
          <div class="sidebar-item" (click)="invokeNavigateTo(section.link)"
            [class]="selectedSection(section.link) ? 'sidebar-section-item-selected':''">
            <mat-icon [svgIcon]="section.icon"></mat-icon>
            <h6 class="sidebar-section-item-text">
              {{section.name}}
            </h6>
          </div>
          }
        </div>
      <div class="sidebar-section-container" *ngIf="sectionAccess['content-management'] === true">
        <h5 class="sidebar-section-text">Crear y subir archivos</h5>

        @for (section of playlistsections; track section) {
          <div class="sidebar-item" (click)="invokeNavigateTo(section.link)"
            [class]="selectedSection(section.link) ? 'sidebar-section-item-playlist-selected':''">
            <mat-icon [svgIcon]="section.icon" [class]="selectedSection(section.link) ? 'clase-icono-playlist':''"></mat-icon>
            <h6 class="sidebar-section-item-text">
              {{section.name}}
            </h6>
          </div>
          }

        @for (section of bannerlistsections; track section) {
            <div class="sidebar-item" (click)="invokeNavigateTo(section.link)"
              [class]="selectedSection(section.link) ? 'sidebar-section-item-playlist-selected':''">
              <mat-icon [svgIcon]="section.icon"></mat-icon>
              <h6 class="sidebar-section-item-text">
                {{section.name}}
              </h6>
            </div>
        }
        @for (section of cargacancionesections; track section) {
        <div class="sidebar-item" (click)="invokeNavigateTo(section.link)"
          [class]="selectedSection(section.link) ? 'sidebar-section-item-cancion-selected':''">
          <mat-icon [svgIcon]="section.icon"></mat-icon>
          <h6 class="sidebar-section-item-text">
            {{section.name}}
          </h6>
        </div>
        }
    </div>

     <div class="sidebar-section-container" *ngIf="sectionAccess['record-label'] === true">
        <h5 class="sidebar-section-text">Dashboards</h5>
        @for (section of recordLabelSections; track section) {
        <div class="sidebar-item" (click)="invokeNavigateTo(section.link)"
          [class]="selectedSection(section.link) ? 'sidebar-section-item-selected':''">
          @if(section.customIcon) {
          <mat-icon [svgIcon]="section.icon"></mat-icon>
          }
          @else {
          <mat-icon class="material-symbols-outlined">{{section.icon}}</mat-icon>
          }
          <h6 class="sidebar-section-item-text">
            {{section.name}}
          </h6>
        </div>
        }
    </div>


    <div class="sidebar-section-container" *ngIf="sectionAccess['content-management'] === true">
      <h5 class="sidebar-section-text">Modo radio</h5>
      @for (section of radioSections; track section) {
        <div class="sidebar-item" (click)="invokeNavigateTo(section.link)"
          [class]="selectedSection(section.link) ? 'sidebar-section-item-selected':''">
          @if(section.customIcon) {
            <mat-icon [svgIcon]="section.icon"></mat-icon>
          }
          @else {
            <mat-icon class="material-symbols-outlined">{{section.icon}}</mat-icon>
          }
          <h6 class="sidebar-section-item-text">
            {{section.name}}
          </h6>
        </div>
    }
    </div>
    <div class="sidebar-section-container" *ngIf="sectionAccess['artist'] === true">
          <h5 class="sidebar-section-text">Dashboards</h5>
          @for (section of artistSections; track section) {
          <div class="sidebar-item" (click)="invokeNavigateTo(section.link)"
            [class]="selectedSection(section.link) ? 'sidebar-section-item-selected':''">
            <mat-icon [svgIcon]="section.icon"></mat-icon>
            <h6 class="sidebar-section-item-text">
              {{section.name}}
            </h6>
          </div>
          }
        </div>
      <div class="sidebar-section-container">
        <h5 class="sidebar-section-text">Cuenta</h5>
        <div class="sidebar-item" (click)="invokeLogout()">
          <mat-icon svgIcon="sign_out_icon"></mat-icon>
          <h6 class="sidebar-section-item-text">
            Cerrar Sesión
          </h6>
        </div>
      </div>
    </div>
  </mat-grid-tile>
  <mat-grid-tile [colspan]="1" [rowspan]="2">
    <div
      style="
        height: 95%;
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: center;
      "
    >
      <app-footer-content style="align-self: flex-end;"></app-footer-content>
    </div>
  </mat-grid-tile>
</mat-grid-list>