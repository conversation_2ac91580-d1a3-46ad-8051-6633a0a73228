import { inject } from '@angular/core';
import { CanActivateChildFn } from '@angular/router';
import { map } from 'rxjs';
import { RoutingService } from '../services/routing-service';
import { UserProfileService } from '../services/user-profile-service';
import { jwtCookieKey } from '../../constants/jwt';
import { CookieService } from 'ngx-cookie-service';

const authChildGuard: CanActivateChildFn  = (route, state) => {
  const userProfileService = inject(UserProfileService);
  const routingService = inject(RoutingService);
  const cookieService = inject(CookieService);
  return userProfileService.getProfile().pipe(
    map((res) => {
      if (res === undefined || cookieService.get(jwtCookieKey) === null){
        userProfileService.logout();
        routingService.add('/login');
        return false;
      };
      return true;
    })
  );
};

export default authChildGuard;
