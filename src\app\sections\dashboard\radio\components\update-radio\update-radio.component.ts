import { Component } from '@angular/core';
import { RoutingService } from '../../../../../services/routing-service';
import { RadioService } from '../../../../../services/radio-service';

@Component({
  selector: 'app-update-radio',
  templateUrl: './update-radio.component.html',
  styleUrl: './update-radio.component.css'
})
export class UpdateRadioComponent {
  constructor(private readonly routingService: RoutingService, private readonly radioService: RadioService) { }

  onBackClick() {
    this.routingService.add('/dashboard/radio');
  }
}
