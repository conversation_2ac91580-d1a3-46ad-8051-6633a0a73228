import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UserProfileService } from '../../../services/user-profile-service';
import { RoutingService } from '../../../services/routing-service';
import { Roles } from '../../../types/auth';

interface SectionLink {
  name: string;
  link: string;
  icon: string;
  disabled: boolean;
  customIcon: boolean;
}

interface SectionLink {
  name: string;
  link: string;
  icon: string;
  disabled: boolean;
  customIcon: boolean;
}
@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.css',
})
export class SidebarComponent implements OnInit {
  @Input() currentRoute='';
  @Output('navigateTo') navigateTo: EventEmitter<any> = new EventEmitter();

  availableSections = [
    {
      name: 'Overview',
      link: 'dashboard/overview',
      icon:'overview_icon',
      disabled:false,
    },
    {
      name: 'Reproducciones',
      link: 'dashboard/reproductions',
      icon:'reproductions_s_icon',
      disabled:true,
    },
  ];

  cargacancionesections = [
    {
      name: '<PERSON>gar canciones',
      link: 'dashboard/carga-canciones',
      icon: 'uploadns_icon',
      iconselect: 'upload_icon',
      disabled: true,
    }
  ];

  playlistsections = [
    {
      name: 'Playlist',
      link: 'dashboard/play-list',
      icon: 'playlistunselect_icon',
      iconselect: 'playlistselect_icon',
      disabled: true,
    }
  ];
  

  radioSections: SectionLink[] = [
    {
      name: 'Radios',
      link: 'dashboard/radio',
      icon: 'radio',
      disabled: true,
      customIcon:false
    },
    /*{
      name: 'Micros Aqustico',
      link: 'dashboard/radio',
      icon: 'music_cast',
      disabled: true,
      customIcon:false
    },
    {
      name: 'Anunciantes',
      link: 'dashboard/radio',
      icon: 'ads_click',
      disabled: true,
      customIcon:false
    }*/
  ];

  bannerlistsections = [
    {
      name: 'Banners',
      link: 'dashboard/banner-list',
      icon: 'banner_icon',
      disabled: true,
    }
  ];

  artistSections = [
     {
      name: 'Reproducciones',
      link: 'dashboard/reproductions',
      icon:'reproductions_s_icon',
      disabled:true,
    },
  ];

  recordLabelSections: SectionLink[] = [
    {
      name: 'Reproducciones',
      link: 'dashboard/reproductions',
      icon: 'music_plus_note_icon',
      disabled: true,
      customIcon:true
    },
    /*{
      name: 'Micros Aqustico',
      link: 'dashboard/radio',
      icon: 'music_cast',
      disabled: true,
      customIcon:false
    },
    {
      name: 'Anunciantes',
      link: 'dashboard/radio',
      icon: 'ads_click',
      disabled: true,
      customIcon:false
    }*/
  ];

  
  sectionAccessList: {[prop:string]:Roles[]} = {
    'dashboards':[Roles.COMMERCIAL],
    'content-management':[Roles.ADMIN],
    'artist':[Roles.ARTIST],
    'record-label':[Roles.RECORD_LABEL],
  }
  
  public sectionAccess: {[prop:string]:Boolean} = {};
  
  constructor(private routingService: RoutingService, private userProfileService: UserProfileService) {}

  selectedSection(sectionUrl:string) {
    return this.currentRoute.includes(sectionUrl);
  }

  invokeNavigateTo(url: string) {
    this.navigateTo.emit(url);
  }

  invokeLogout() {
    this.userProfileService.logout();
    this.routingService.add('/');
  }

  checkSectionAccess(section:string): boolean {
    return this.getUserAccess(this.sectionAccessList[section]);
  }

  getUserAccess(accessList:Roles[]): boolean {
    const profile = this.userProfileService.getProfileSync();
    return accessList.includes(profile?.role as Roles)
  }  

  ngOnInit(): void {  
    const profile = this.userProfileService.getProfileSync();
    this.sectionAccess = Object.fromEntries(Object.entries(this.sectionAccessList).map(([k,v]) => [k,v.includes(profile?.role as Roles)]));
  }
}
