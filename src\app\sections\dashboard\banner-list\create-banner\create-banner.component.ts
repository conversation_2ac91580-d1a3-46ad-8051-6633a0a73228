import {
  Component,
  Input,
  OnInit,
  <PERSON><PERSON><PERSON><PERSON>n,
  <PERSON><PERSON><PERSON><PERSON>,
  ChangeDetectorRef,
} from "@angular/core"
import {
  AbstractControl,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms"
import { MatSnackBar } from "@angular/material/snack-bar"
import { CrearBannerService } from "../../../../services/crearbanner-service"
import { CustomSnackbarComponent } from "../../../../components/custom-snackbar/custom-snackbar.component"
import { CreateBannerDto } from "../../../../types/banner"
import { MatDialog } from "@angular/material/dialog"
import { ActionConfirmDialogComponent } from "../../../../components/action-confirm-dialog/action-confirm-dialog.component"
import { RoutingService } from "../../../../services/routing-service"
import { SuccessActionDialogComponent } from "../../../../components/success-action-dialog/success-action-dialog.component"
import { BannerState } from "../../../../enums/banner-state.enum"
import { DeleteConfirmDialogComponent } from "../../../../components/delete-confirm-dialog/delete-confirm-dialog.component"
import { BannerService } from "../../../../services/banner-service"
import { ImagePickFieldComponent } from "../image-pick-field/image-pick-field.component"
import { ScheduleBannerModalComponent } from "../../../../components/schedule-banner-modal/schedule-banner-modal.component"
import moment from "moment"

function mimeTypeValidator(
  control: AbstractControl
): { [key: string]: any } | null {
  const file = control.value as File
  if (!file) {
    return null
  }
  const allowedMimeTypes = ["image/png", "image/jpeg", "image/jpg"]
  const isValid = allowedMimeTypes.includes(file.type)
  return isValid ? null : { invalidMimeType: true }
}

@Component({
  selector: "app-create-banner",
  templateUrl: "./create-banner.component.html",
  styleUrl: "./create-banner.component.css",
})
export class CreateBannerComponent implements OnInit {
  @ViewChildren(ImagePickFieldComponent)
  imagePickFields!: QueryList<ImagePickFieldComponent>

  currentBannersPositions: number[] = []
  isProgramming = false
  publicationDate: moment.Moment | null = null
  isUpdateMode = false
  bannerId: number = 0
  ActiveBanner = false

  constructor(
    private CrearBannerService: CrearBannerService,
    private bannerService: BannerService,
    private _snackBar: MatSnackBar,
    private _dialog: MatDialog,
    private routingService: RoutingService,
    private cdr: ChangeDetectorRef
  ) {}

  isValidUrl(control: FormControl): { [s: string]: boolean } | null {
    const urlPattern = /^(http|https):\/\/[^ "]+$/
    const dotPattern = /\./g
    const dotCount = (control.value.match(dotPattern) || []).length
    if (!control.value.match(urlPattern) || dotCount < 1) {
      return { invalidUrl: true }
    }
    return null
  }

  emitSnackbarMessage(message: string, isError: boolean = false) {
    const SNACKBAR_DURATION = 5
    this._snackBar.openFromComponent(CustomSnackbarComponent, {
      duration: SNACKBAR_DURATION * 1000,
      horizontalPosition: "center",
      verticalPosition: "top",
      data: { message },
      panelClass: isError ? ["error-snackbar"] : ["success-snackbar"],
    })
  }

  createBannerForm = new FormGroup({
    nombre: new FormControl<string>("", [
      Validators.required,
      Validators.maxLength(60),
    ]),
    url: new FormControl<string>("", [Validators.required, this.isValidUrl]),
    texto_banner: new FormControl<string>("", Validators.maxLength(60)),
    texto_boton_banner: new FormControl<string>("", [
      Validators.pattern(/^[a-zA-Z\s\u00C0-\u017F]*$/),
      Validators.maxLength(20),
    ]),
    bannerWeb: new FormControl<File | null>(null, [
      Validators.required,
      mimeTypeValidator,
    ]),
    bannerWebExt: new FormControl<File | null>(null, [
      Validators.required,
      mimeTypeValidator,
    ]),
    bannerMob: new FormControl<File | null>(null, [
      Validators.required,
      mimeTypeValidator,
    ]),
    position: new FormControl<number>(1),
    state: new FormControl<BannerState>(BannerState.ACTIVE),
  })

  imageFieldUpdate(fieldValue: { [p: string]: File | null }) {
    const fieldName = Object.keys(fieldValue)[0]
    const file = fieldValue[fieldName]
    if (file) {
      this.createBannerForm.patchValue({ [fieldName]: file })
      this.createBannerForm.get(fieldName)?.updateValueAndValidity()
    }
  }

  onSubmit(event: Event) {
    event.preventDefault()
    if (!this.createBannerForm.valid) {
      this.emitSnackbarMessage(
        "Por favor, complete todos los campos correctamente.",
        true
      )
      return
    }
    if (
      this.currentBannersPositions.includes(
        this.createBannerForm.get("position")!.value!
      )
    ) {
      const dialogRef = this._dialog.open(DeleteConfirmDialogComponent, {
        panelClass: "delete-confirm-dialog-container",
        data: {
          message: `Se reemplazará el banner de la: <p style="color: #1BE2ED !important;">Posición ${
            this.createBannerForm.get("position")!.value
          }</p>`,
          action: "Continuar",
          title: "Reemplazo de banner",
        },
      })

      dialogRef.componentInstance.onActionClick.subscribe(
        (confirmed: boolean) => {
          if (confirmed && !this.isUpdateMode) {
            this.createBanner()
          } else if (confirmed && this.isUpdateMode) {
            this.updateBanner()
          }
        }
      )
    } else {
      if (this.isUpdateMode) {
        this.updateBanner()
      } else {
        this.createBanner()
      }
    }
  }

  createBanner() {
    const createData = this.createBannerForm.value as CreateBannerDto
    if (this.isProgramming && this.publicationDate) {
      createData.state = BannerState.PENDING
      createData.pubication_date = this.publicationDate.toISOString()
    }
    this.CrearBannerService.createBanner(createData).subscribe({
      next: (response) => {
        this.ooProcessSuccess(5000)
        this.routingService.add("dashboard/banner-list")
      },
      error: (error) => {
        this.emitSnackbarMessage(
          error || "Ha ocurrido un error inesperado.",
          true
        )
      },
    })
  }

  updateBanner() {
    const updateData = this.createBannerForm.value as CreateBannerDto
    if (this.isProgramming && this.publicationDate) {
      updateData.state = BannerState.PENDING
      updateData.pubication_date = this.publicationDate.toISOString()
    }
    this.bannerService.updateBanner(this.bannerId, updateData).subscribe({
      next: (response) => {
        this.ooProcessSuccess(5000)
        this.routingService.add("dashboard/banner-list")
      },
      error: (error) => {
        this.emitSnackbarMessage("Ha ocurrido un error inesperado.", true)
      },
    })
  }

  onProcessStop() {
    const dialogRef = this._dialog.open(ActionConfirmDialogComponent, {
      panelClass: "action-confirm-dialog-container",
    })
    dialogRef.componentInstance.onActionClick.subscribe((op: boolean) => {
      if (op) {
        this.routingService.add("dashboard/banner-list")
      }
    })
  }

  ooProcessSuccess(autoCloseDelay?: number) {
    this._dialog.open(SuccessActionDialogComponent, {
      panelClass: "success-dialog-container",
      data: { autoCloseDelay },
    })
  }

  OpenProgramModal() {
    const dialogRef = this._dialog.open<ScheduleBannerModalComponent>(
      ScheduleBannerModalComponent,
      {
        panelClass: "schedule-banner-modal-container",
        data: { actualDate: this.publicationDate },
      }
    )

    dialogRef.componentInstance!.onScheduleConfirm.subscribe(
      (scheduledDateTime: moment.Moment) => {
        this.publicationDate = scheduledDateTime
        this.isProgramming = true
        // Call onSubmit after setting the publication date
        this.onSubmit(new Event("submit"))
      }
    )
  }

  ngOnInit(): void {
    this.createBannerForm.valueChanges.subscribe((value) => {})
    this.bannerService.getBannerPositions().subscribe({
      next: (response) => {
        this.currentBannersPositions = response.body!
      },
      error: (error) => {
        this.emitSnackbarMessage("Ha ocurrido un error inesperado.", true)
      },
    })
    this.isUpdateMode = this.routingService.getCurrentRoute().includes("update")
    if (this.isUpdateMode) {
      this.bannerId = parseInt(
        this.routingService.getCurrentRoute().split("/").pop()!
      )
      this.bannerService.getBanner(this.bannerId).subscribe({
        next: async (response) => {
          this.ActiveBanner = response.body!.state === BannerState.ACTIVE
          response.body!.state = BannerState.ACTIVE
          const files = await this.bannerService.getBannerImage(this.bannerId)
          if (response.body!.publication_date)
            this.publicationDate = moment(response.body!.publication_date)
          this.createBannerForm.patchValue({
            url: response.body!.redirect_to,
            texto_banner: response.body!.banner_text,
            texto_boton_banner: response.body!.banner_action_text,
            bannerWeb: files[0],
            bannerMob: files[1],
            bannerWebExt: files[2],
            ...response.body!,
          })
        },
        error: (error) => {
          this.emitSnackbarMessage("Ha ocurrido un error inesperado.", true)
        },
      })
    }
  }
}
