import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';

@Injectable({
    providedIn: 'root',
  })
export class GoogleDataService {
  constructor(private http:HttpClient){}
  
  private apiUrl = environment.apiUrl;
  private serviceUrl = `${this.apiUrl}/ga4-aqs`;

  public getLocationTrafficData(initDate:string,limitDate:string){
    return this.http.get(`${this.serviceUrl}/location-traffic`,{observe: 'response',params:{initDate,limitDate} });
  }

  public getDeviceTrafficData(initDate:string,limitDate:string){
    return this.http.get(`${this.serviceUrl}/device-traffic`,{observe: 'response',params:{initDate,limitDate} });
  }
}