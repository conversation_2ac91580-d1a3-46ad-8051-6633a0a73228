import { Injectable } from '@angular/core';
import {
  HttpEvent,
  HttpInterceptor,
  HttpHandler,
  HttpRequest,
  HttpErrorResponse,
} from '@angular/common/http';

import { Observable, catchError, throwError } from 'rxjs';
import { UserProfileService } from '../services/user-profile-service';
import { RoutingService } from '../services/routing-service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(
    private userProfileService: UserProfileService,
    private routingService: RoutingService
  ) {}
  handleHttpError = (err: HttpErrorResponse) => {
    let errorMessage = 'Ha ocurrido un error desconocido';
    if (err.status === 401) {
      this.userProfileService.logout();
      this.routingService.add('/');
    }
    return throwError(() => err);
  }
  intercept(
    req: HttpRequest<any>,
    next: <PERSON>ttpHand<PERSON>
  ): Observable<HttpEvent<any>> {
    return next
      .handle(
        req.clone({
          withCredentials: true,
          setHeaders:{
            'Authorization':`Bearer ${localStorage.getItem('AQS-AUTH')}`
          }
        })
      )
      .pipe(catchError(this.handleHttpError));
  }
}
