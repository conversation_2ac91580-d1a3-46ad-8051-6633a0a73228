:host {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}


.app {
  margin-top: 50px;
  margin-left: 92px;
  background-color: transparent;
  color: #fff;
  max-width: 100%; 
  padding: 0px;
  border-radius: 10px;
}



.icon {
  display: flex; 
  flex-direction: column; 
  justify-content: center; 
  align-items: center; 
  width: 252px;
  height: 252px;
  border-radius: 15.12px;
  background: var(--AquaLight, #6BDCCC);
  margin-right: 20px;
}


mat-icon[svgIcon="icono"] {
  width: 126px; 
  height: 126px; 
}


.top-section {
    display: flex;
    justify-content: start;
    /*margin-left: 150px;*/
}

.inputs {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.input-field {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
}

label {
    margin-bottom: 5px;
}

input[type="text"], textarea {
    padding: 10px;
    border: none;
    border-radius: 5px;
    width: 735px; /* Ancho del campo de entrada */
    height: 50px; /* Altura del campo de entrada */
    border-radius: var(--12, 12px);
    background: rgba(255, 255, 255, 0.15);
    color: #FFF;
    font-family: "Visby CF";
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0.14px;
}


button[type="submit"] {
  border-radius: 12px;
  opacity: 0.5;
  background: #00E5EF;
  border-color: #00E5EF;
  color: #000;
  text-align: center;
  font-family: "Visby CF";
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  width: 149px;
  height: 49px;
}
  
input::placeholder {
  color: #FFF;
  font-family: "Visby CF";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.14px;
}

textarea::placeholder {
  color: #FFF;
  font-family: "Visby CF";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.14px;
}

.input-titulo{
  color: #FFF;
  font-feature-settings: 'clig' off, 'liga' off;
  font-family: "Visby CF";
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px; 
}

.textarea-descripcion{
  width: 341px;
  height: 124px;
}

.input-field-textarea {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

textarea {
  height: 124px; 
  
}

.message{
  margin: 43 auto;
  text-align: center; 
  width: 100%;
  color: var(--White, #FFF);
  font-family: "Visby CF";
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 25.2px; 
}

.area-button-submit{
  width: 735px;
  height: 49px;
  display: flex; 
  justify-content:right; 
  align-items: right; 
  margin-top: 67px;
}

.image_cancion{
  display: flex; 
  justify-content: center; 
  align-items: center; 
  width: 735px;
  height: 180px;
  margin-top: -10px;
  margin-bottom: -10px;
}

mat-icon[svgIcon="list_alt_add_cancion_icon"] {
  width: 100px; 
  height: 100px; 
}

.error {
  color: #f44336; 
}


textarea.ng-invalid.ng-touched {
  box-shadow: none;
  outline: none;
  border: none;
}

textarea:invalid {
  box-shadow: none;
}

.texto_imagen {
  color: var(--DarkBlueBG, #201F28);
  font-family: "Visby CF";
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 25.2px; 
  text-align: center; 
  margin-top: 20px; 
}

.chip-list {
  max-width: 835px; 
  overflow-x: auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: start;
}


.chip-list mat-chip-option {
  background-color: #DF08A8; 
  color: #FFF; 
  border-radius: 12px; 
  height: 42px; 
  line-height: 42px; 
  margin: 5px; 
  padding: 0 10px; 
  text-align: center; 
  width: 222px; 
}

.close_chip {
  margin-top: 8px;
  margin-bottom: 8px;
  margin-left: 10px;
  margin-right: 10px;
}

.form-content {
  display: flex; 
}

.other-fields {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.image-upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.valor-procesado {
  color: #FFF;
  font-family: "Visby CF";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.14px;
  margin-bottom: 30px;
  margin-left: 16px;
}

.btn-acustico {
  width: 169px;
  height: 49px;
  background-color: transparent;
  border-radius: 12px;
  border: 2px solid #00E5EF;
  box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.18);
  color: #00E5EF;
  text-align: center;
  font-family: Visby CF;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  cursor: pointer;
}

.url-playlist{
  color: #00E5EF;
  text-align: center;
  font-family: "Visby CF";
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  text-decoration: none;
}

.alinear-icono-texto {
  display: flex;
  align-items: center;
}

.section-title {
  color: var(--White, #FFF);
  font-family: "Visby CF";
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px; /* 142.857% */
  padding-bottom: 48px;
}
