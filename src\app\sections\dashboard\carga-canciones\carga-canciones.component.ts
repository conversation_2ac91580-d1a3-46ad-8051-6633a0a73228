import { Component, ElementRef, ViewChild, AfterViewInit, OnInit } from '@angular/core';
import * as fs from 'file-saver'
import { UploadService } from '../../../services/upload.service';
import { Location } from '@angular/common';
import { of } from 'rxjs';
import { delay } from 'rxjs/operators';
import { CustomSnackbarComponent } from '../../../components/custom-snackbar/custom-snackbar.component';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-carga-canciones',
  standalone: false,
  templateUrl: './carga-canciones.component.html',
  styleUrl: './carga-canciones.component.css'
})
export class CargaCancionesComponent implements AfterViewInit,OnInit {

  draggedFile: File | null = null;

  //@ViewChild('fileUpload') fileUpload!: ElementRef;
  @ViewChild('fileUpload', { static: false }) fileUpload!: ElementRef; 
  
  

  inputfilevalid!: HTMLInputElement;
  textup1 = "Arrastra el archivo .csv aquí";
  textup2 = "- o -";
  status = '';
  boton = 'Selecciona desde el equipo';
  boton2 = 'Subir archivo';
  index= 1;
  newFile:any[] = []
  newErrorFile:any[] = []
  arraySong:string[] = [];
  durationInSeconds = 10;
  
  ngOnInit(): void {
    setTimeout(() => {
      this.inputfilevalid = this.fileUpload.nativeElement;
    }, 0);
    //let audioMedia = document.getElementById('upload-audio') as HTMLMediaElement
    /*audioMedia.addEventListener('loadedmetadata', this.handleLoadedMetadata)
    audioMedia.addEventListener('error', this.handleError)*/
  }
  

  initializeFileUpload() {
    if (this.fileUpload) {
      this.inputfilevalid = this.fileUpload.nativeElement;
    } else {
      console.error('fileUpload is not yet defined');
    }
  }

  ngAfterViewInit() {
    this.inputfilevalid = this.fileUpload.nativeElement;
    this.initializeFileUpload();
  }
  

  constructor(private uploadService: UploadService,private location: Location,private _snackBar: MatSnackBar){}

  handleLoadedMetadata = (event:any) => {
      this.newFile.push(`${this.arraySong[this.index]};${event.target.duration}`);
      this.processNextSong();  
  }
    
  handleError = (event:any) => {
      this.newErrorFile.push(`${this.arraySong[this.index]}`);
      this.processNextSong();
  }
      

  onFilesSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    this.inputfilevalid = input;
    if (input.files && input.files.length > 0) {
      for (let i = 0; i < input.files.length; i++) {
        this.readFile(input.files[i]);
      }
    }
  }

  

  onUploadClick() {
    if(this.status == 'errorupfile'){
      this.textup1 = "Arrastra el archivo .csv aquí";
      this.textup2 = "- o -";
      this. status = '';
      this.boton = 'Selecciona desde el equipo';
      this.boton2 = 'Subir archivo';
      this.index= 1;
      this.newFile = []
      this.newErrorFile= []
      this.arraySong= [];
      this.resetValues;
      return;
    }
    this.status = 'uploading';
    this.textup1 = 'Subiendo archivo...';
    if (this.draggedFile) {
      // Si hay un archivo arrastrado y soltado, léelo y procésalo
      this.readFiles(this.draggedFile);
      this.draggedFile = null; // Restablece la referencia al archivo arrastrado y soltado
    } else if (this.inputfilevalid.files && this.inputfilevalid.files.length > 0) {
      // Si no hay un archivo arrastrado y soltado, pero hay un archivo seleccionado a través del input, léelo y procésalo
      for (let i = 0; i < this.inputfilevalid.files.length; i++) {
        this.readFiles(this.inputfilevalid.files[i]);
      }
    }
  }
  


  validFile(){
    if (this.inputfilevalid.files && this.inputfilevalid.files.length > 0) {
      for (let i = 0; i < this.inputfilevalid.files.length; i++) {
        this.readFiles(this.inputfilevalid.files[i]);
      }
    }
  }

  

  onFileDropped(event: DragEvent) {
      event.preventDefault();
    
      if (event.dataTransfer && event.dataTransfer.items && event.dataTransfer.items.length > 0) {
        for (let i = 0; i < event.dataTransfer.items.length; i++) {
          const file = event.dataTransfer.items[i].getAsFile();
          if (file) {
            this.readFile(file);
            this.draggedFile = file; // Almacena una referencia al archivo arrastrado y soltado
          }
        }
      }
  }
  
  
  onDragOver(event: DragEvent) {
    event.preventDefault();
  }

  readFile(file: File) {
  this.textup1 = "Procesando...";
  this.textup2 = "";
  this.status = 'loading';

  const reader = new FileReader();
  reader.onload = (e: any) => {
    const contents: string = e.target.result;
    if (contents.trim().length === 0) {
      console.log('El archivo', file.name, 'está vacío');
    } else {
      console.log('El archivo', file.name, 'tiene contenido');
    }

    // Simula un retraso antes de procesar el archivo
    of(null)
      .pipe(delay(2000))  // Cambia este valor al tiempo de espera deseado en milisegundos
      .subscribe(() => {
        this.textup1 += file.name + "\n";  // Agrega el nombre del archivo a textup1
        this.textup2 = "";
        this.status = 'success';
      });
  };

  // Agrega un manejador de errores
  reader.onerror = (e: any) => {
    console.error('Error al leer el archivo:', e);
    this.textup1 = "Ha ocurrido un error al leer el archivo.";
    this.status = 'error';
  };

  reader.readAsText(file);
}

async audioUrlCheck(data: string): Promise<[string,number]> {
  return new Promise((resolve, reject) => {
    const audioHandler = new Audio();
    audioHandler.setAttribute('src', data);
    audioHandler.addEventListener('loadedmetadata', (event:any) => {
      resolve([data, audioHandler.duration])
      audioHandler.removeAttribute('src');
      audioHandler.load();
    })
    audioHandler.addEventListener('error', (event:any) => {
      reject(data);
    });
    audioHandler.load();
  });
}

  async dataProcess(data:string[]){
    this.newFile.push(`productId;name;artist;category;album;caratula;demo;cancion;genero;date;sello;duration`);
    this.newErrorFile.push(`productId;name;artist;category;album;caratula;demo;cancion;genero;date;sello;error_origin`);
    for(const song of data) {
      const songData = song.split(/,|;/);
      let _demoCheck:[string,number];
      let _originalCheck:[string,number];
      try { 
        _demoCheck = await this.audioUrlCheck(songData[6]);
        _originalCheck = await this.audioUrlCheck(songData[7]);
        this.newFile.push(`${song};${_originalCheck[1]}`);
      }
      catch (error) {
        console.error('Error al procesar la canción:', songData, error);
        this.newErrorFile.push(`${song};${error}`);
      }
    }
    this.downloadFile();
  }

  readFiles(file: any) {
    const reader = new FileReader()
    reader.addEventListener('load', (event:any) => {
      const result:string[] = event.target.result.split(/\r\n|\r|\n/gm).filter((line:any) => line !== '');
      this.dataProcess(result.filter((e,_i) => _i > 0));
    });
    reader.readAsText(file);
  }


  loadSong(songData: string[]): void {
    let audioMedia = document.getElementById('upload-audio') as HTMLMediaElement;
    if (songData.length > 7 && songData[7]) {
      audioMedia.src = songData[7].replace(/"/g,'');
      audioMedia.load();
    } else {
      console.error('Error: Algún archivo subido contiene elementos que no son de audio.');
      this.textup1 = "Ha ocurrido un problema al subir el archivo.";
      this.status = 'errorupfile';
      this.boton2 = 'Intentar nuevamente';
    }
  }
  
  

  getAudioDuration(arraySong:string[]){
    this.arraySong = arraySong;
    let songData  = [];
    this.newFile.push(`productId;name;artist;category;album;caratula;demo;cancion;genero;date;sello;duration`)
    songData  = arraySong[this.index].split(';');
    this.loadSong(songData);
  } 


  processNextSong(){
    if (this.index < this.arraySong.length - 1) {
      this.index++;
      const songData = this.arraySong[this.index].split(';');
      this.loadSong(songData);
    } else {
      // Eliminar los controladores de eventos existentes
      this.downloadFile();
    }
  };

  

  public async downloadFile(){
    
        const fileData = this.newFile.join('\n')
        const fileErrData = this.newErrorFile.join('\n')
        const date  = new Date().toISOString().split('T')[0]
      
        const fileName = 'aqustico-canciones'
        
        const file = new Blob([fileData])
        const file2 = new Blob([fileErrData])
        console.log('Termino la descarga...... ' + " Arch correcto: " + file.size + " Arch incorrecto: " + file2.size);
        
      
          fs.saveAs(file,`${fileName}-${date}-completo.csv`)
          fs.saveAs(file2,`${fileName}-${date}-completo-error.csv`)
      
        
        // Llama al método uploadFile() con el objeto File
        if (file.size > 0 || file2.size > 0) {
          // Convierte el Blob en un File
          const fileToUpload = new File([file], `${fileName}-${date}-completo.csv`);
          console.log('Termino la descarga..enviando al servicio.... ' + " Arch correcto: " + file.size );
          console.log(fileToUpload);
          this.uploadService.uploadFile(fileToUpload).subscribe({
            next: (response) => {
              console.log('Archivo subido con éxito:', response);
              if(file2.size > 0){
                this.textup1 = "Su archivo ha sido subido con algunos errores.";
                this.status = 'successfile';
                this.boton = 'Cargar otro archivo';
              }else{
                this.textup1 = "Su archivo ha sido subido exitosamente.";
                this.status = 'successfile';
                this.boton = 'Cargar otro archivo';
              }
              this.resetValues(); // Mueve esta línea aquí para que se ejecute después de que se haya completado la carga del archivo
            },
            error: (error) => {
              console.error('Error al subir el archivo:', error);
              this.textup1 = "Ha ocurrido un problema al subir el archivo.";
              this.status = 'errorupfile';
              this.boton2 = 'Intentar nuevamente';
              this._snackBar.openFromComponent(CustomSnackbarComponent, {
                duration: this.durationInSeconds * 1000,
                horizontalPosition:"center",
                verticalPosition:"top",
                data:{ message:error},
                panelClass:['error-snackbar'],
              });
            }
          });
        }
        
  }
      

  
  resetValues() {
    this.index = 1;
    this.newFile = [];
    this.newErrorFile = [];
    this.status = '';
  }
 
  
}
