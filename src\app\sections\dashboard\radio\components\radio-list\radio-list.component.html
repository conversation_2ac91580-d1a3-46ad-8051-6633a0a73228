<div class="section-header">
  <p>Radios</p>
  <button mat-flat-button class="common-flat-button on-hover-flat-button" (click)="onNewRadioClick()">Crear Radio</button>
</div>
<div class="section-content">
  <table mat-table [dataSource]="dataSource" matSort class="radio-list-table">
    <ng-container matColumnDef="id">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>ID</th>
      <td mat-cell *matCellDef="let row"> {{ row.id }} </td>
    </ng-container>
    <ng-container matColumnDef="title">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>Título</th>
      <td mat-cell *matCellDef="let row"> {{ row.title }} </td>
    </ng-container>
    <ng-container matColumnDef="author">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>Autor</th>
      <td mat-cell *matCellDef="let row"> {{ row.author }} </td>
    </ng-container>
    <ng-container matColumnDef="actions">
      <th mat-header-cell *matHeaderCellDef aria-label="row actions" class="actions-header">Acciones</th>
      <td mat-cell *matCellDef="let row;let i = index" class="actions-column-row">
        <button *ngIf="row.content_status === 1" mat-icon-button class="action-button-icon on-hover-overlay-icon-button">
          <mat-icon>edit_note</mat-icon>
        </button>
        <button *ngIf="row.content_status === 1 && row.is_public === false" mat-fab extended class="action-button-icon-ext unfinished-radio on-hover-overlay" (click)="onPublishRadioClick(row)">
          <mat-icon>edit_note</mat-icon>
          Publicar
        </button>
        <button *ngIf="row.content_status === 0" mat-fab extended class="action-button-icon-ext on-hover-overlay">
          <mat-icon>edit_note</mat-icon>
          Continuar Creación
        </button>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    <tr class="mat-row" *matNoDataRow>
      <!--<td class="mat-cell" colspan="4">No data matching the filter "{{input.value}}"</td>-->
    </tr>
  </table>
  <mat-paginator [pageSizeOptions]="pageSizeOptions" [pageSize]="pageSize" [pageIndex]="pageIndex"
  [length]="totalElements" showFirstLastButtons aria-label="Select page of radios"
    itemsPerPageLabel="Filas por página" class="radio-list-paginator" (page)="onPageChange($event)"></mat-paginator>
</div>