import { PlatformMediaTypeLabel } from "./platform-media";

export enum RadioStreamContentStatus {
  DRAFT = 0,
  COMPLETED = 1,
}

export interface ContentConfig {
  artistIds?: number[];
  genreIds?: number[];
  albumIds?: number[];
  songIds?: number[];
}

export interface RadioType {
  id: number;
  title: string;
  author: string;
  content_status: RadioStreamContentStatus;
  is_public: boolean;
}

export interface RadioTypeDetail extends RadioType {
    createdAt: Date;
    lastUpdateAt: Date;
    coverImageUrl: string;
    contentConfig: ContentConfig;
}

export interface CreateRadioData {
    title: string;
    author: string;
    coverImageUrl: File;
    contentConfig: ContentConfig;
    platformMediaContent:number[];
    is_public?: boolean;
}

export enum LookupType {
  SONG = 0,
  GENRE = 1,
  ARTIST = 2,
  PLATFORM_MEDIA = 3,
}
export interface LookupOption {
  name: string;
  optionType: LookupType;
  optionId: number;
  cover: string;
  label: string;
  mediaTypeLabel?: PlatformMediaTypeLabel;
}
