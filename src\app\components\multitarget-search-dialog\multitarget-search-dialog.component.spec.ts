import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MultitargetSearchDialogComponent } from './multitarget-search-dialog.component';

describe('MultitargetSearchDialogComponent', () => {
  let component: MultitargetSearchDialogComponent;
  let fixture: ComponentFixture<MultitargetSearchDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MultitargetSearchDialogComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(MultitargetSearchDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
