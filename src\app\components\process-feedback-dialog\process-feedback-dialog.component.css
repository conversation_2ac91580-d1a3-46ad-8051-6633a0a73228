::ng-deep .process-feedback-dialog-container {
    --mat-dialog-container-max-width: 722px;
    --mat-dialog-container-min-width: 722px;
    --mat-dialog-content-padding: 96px 56px;
    --mat-dialog-actions-padding: 0px 56px 56px 56px;
    --mdc-dialog-container-color: #1A191F;
}

mat-dialog-content {
    align-items: center;
    display: flex;
    flex-direction: column;
}

.feedback-icon {
    font-size: 72px;
    width: 72px;
    height: 72px;
    margin: 0px auto 16px auto;

    &.positive {
        color: #1BE2ED;
    }
    &.negative {
        color: #FF4D4F;
    }
}

.feedback-message {
    text-align: center;
    color: #ffffff;
    font-family: "Visby CF";
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 38px;
}