<img width="48" height="48" src="{{lookupOption.cover}}">
<div class="option-name">
    <h5>{{lookupOption.name}}</h5>
    <h6>{{lookupOption.label}}</h6>
    @switch (lookupOption.mediaTypeLabel) {
        @case (PlatformMediaTypeLabel.ADS) {
            <h6>Anuncio</h6>
        }
        @case (PlatformMediaTypeLabel.ARTIST_CLIP) {
            <h6>Clip de artista</h6>
        }
        @case (PlatformMediaTypeLabel.PLATFORM_CONTENT) {
            <h6>Micro de contenido</h6>
        }
        @case (PlatformMediaTypeLabel.WELCOME_PLATFORM_CONTENT) {
            <h6>Micro de bienvenida</h6>
        }
    }
</div>
<mat-icon [class.active]="is_selected" (click)="onUpdateClick()" class="material-symbols-outlined">{{ is_selected ? 'check_circle':'add_circle_outline'}}</mat-icon>