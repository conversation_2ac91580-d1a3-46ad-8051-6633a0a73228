<style>
    .main-container {
        align-items: center;
        display: flex;
        flex-direction: row;
    }
    .dashboard-container {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
    }
    :host {
        width: 100%;
        height: 100%;
        background-color: #201F28;
        background-image:radial-gradient(circle 59vh  at 4% 102.5%,#550DC5,transparent),radial-gradient(circle 26.1vw at 104.5% -5.5%,#550DC5,#1f2822);
    }
</style>
<div class="main-container">
    <mat-grid-list cols="128" rowHeight="100vh" style="width: 100%;">
        <mat-grid-tile [colspan]="19">
            <app-sidebar (navigateTo)="navigateTo($event)" [currentRoute]="currentRoute"></app-sidebar>
        </mat-grid-tile>
        <mat-grid-tile [colspan]="109">
            <div style="height: 100%;width: 100%;display: flex;flex-direction: column;">
                <app-header (navigateTo)="navigateTo($event)" [currentRoute]="currentRoute"></app-header>
                <router-outlet/>
            </div>
        </mat-grid-tile>
    </mat-grid-list>
</div>