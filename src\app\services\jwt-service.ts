import { Injectable } from '@angular/core';
import { JwtHelperService } from '@auth0/angular-jwt';
import { jwtCookieKey } from '../../constants/jwt';
import { CookieService } from 'ngx-cookie-service';

@Injectable({
    providedIn: 'root',
})
export class JwtService {
  constructor(private jwtHelper:JwtHelperService,private cookieService:CookieService){}

  isAuthenticated(): boolean {
    const token = this.cookieService.get(jwtCookieKey);
    return !this.jwtHelper.isTokenExpired(token);
  }
}