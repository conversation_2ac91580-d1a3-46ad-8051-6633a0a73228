@import './../../../components/multitarget-autocomplete.component.css';
@import './../../../components/custom-date-picker.css';
@import './../../../shared/styles/custom-search-button.css';
@import './../../../shared/styles/common-table.css';

:host {
    padding: 24px 0px 0px 80px;
    gap: 40px;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow-y: auto;
}

.utilites-container {
    display: flex;
    width: 100%;
    align-items: flex-end;
}

.date-utils-container {
    flex: 1;
    display: flex;
    justify-content: flex-end;
}

.data-container {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    padding-bottom: 48px;
}

.key-overview-container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 20px;
    width: 100%;
}

.data-table-container {
    flex: 1;
}

.data-actions-container {
    width: 17.5%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
}

.utilites-container,
.data-table-container,
.key-overview-container {
    width: 82.5%;
}

.custom-sort {
    width: 16px;
    height: 16px;
    margin-left: 6px;
}

.custom-sort-inverse {
    transform: rotate(180deg);
}

::ng-deep .table-settings-sort {
    display: none;
}


::ng-deep .mat-sort-header-arrow {
    display: none !important;
}

::ng-deep .mat-sort-header-container :last-child.ng-star-inserted {
    color: white;
}

.custom-date-picker {
    --mat-form-field-container-vertical-padding: 4px;
}

.key-overview {
    gap: 12px;
    background-color: rgba(229, 236, 246, 1);
    min-height: 91px;
    border-radius: 12px;
    padding: 24px;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: auto auto;
}

.key-overview-header {
    display: flex;
    justify-content: space-between;
    color: rgba(28, 20, 104, 1);

    h4 {
        margin: 0px;
        font-family: "Visby CF";
        font-size: 12px;
        font-style: normal;
        font-weight: 700;
        line-height: 18px;
    }
}

.key-overview-content {
    display: flex;
    flex-wrap: wrap;

    h2,
    h3,
    h6 {
        color: #201F28;
        font-family: "Visby CF";
        font-style: normal;
        line-height: normal;
        margin: 0px;
    }

    h3 {
        font-size: 16px;
        font-weight: 500;
        width: 100%;
    }

    h2 {
        font-size: 26px;
        font-weight: 600;
        text-align: center;
    }

    h6 {
        font-size: 12px;
        font-weight: 400;
        opacity: 0.7;
        margin-left: 4px !important;
        align-self: flex-end;
    }
}

.album-icon {
    height: 16px;
    width: 16px;
    cursor: pointer;
}