import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { CreateRadioData, RadioType } from '../types/radio';
import { Observable, Subject } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class RadioService {
    isRollbackActive$: Subject<boolean> = new Subject<boolean>();
    constructor(private http: HttpClient) {}

    private apiUrl = environment.apiUrl;
    private serviceUrl = `${this.apiUrl}/radio-stream`;
    getRadios() : Observable<HttpResponse<RadioType[]>> {
        return this.http.get<RadioType[]>(this.serviceUrl, { observe: 'response', responseType: 'json' });
    }

    createRadio({coverImageUrl,...radio}: CreateRadioData) : Observable<HttpResponse<RadioType>> {
        const formData = new FormData();
        formData.append('title', radio.title);
        formData.append('author', radio.author);
        formData.append('contentConfig', JSON.stringify(radio.contentConfig));
        formData.append('platformMediaContent', JSON.stringify(radio.platformMediaContent));
        if(radio.is_public !== undefined) {
            formData.append('is_public', radio.is_public.toString());
        }
        formData.append('coverImageUrl', coverImageUrl);
        return this.http.post<RadioType>(this.serviceUrl, formData, { 
            observe: 'response', 
            responseType: 'json' 
        });
    }

    patchRadioPublishState(radioId: number,publishState:boolean) : Observable<HttpResponse<any>> {
        return this.http.post(`${this.serviceUrl}/publish-state`, {id:radioId,is_public:publishState}, { 
            observe: 'response', 
            responseType: 'json' 
        });
    }

    setRollback(isActive: boolean) {
        this.isRollbackActive$.next(isActive);
    }
}