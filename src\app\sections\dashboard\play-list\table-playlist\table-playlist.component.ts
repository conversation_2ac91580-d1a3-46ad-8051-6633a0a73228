import {AfterViewInit, Component, ViewChild, Output, EventEmitter} from '@angular/core';
import {MatPaginator} from '@angular/material/paginator';
import {MatSort} from '@angular/material/sort';
import {MatTableDataSource} from '@angular/material/table';
import {PlaylistService, Playlist} from '../../../../services/playlist-service';
import { CustomSnackbarComponent } from '../../../../components/custom-snackbar/custom-snackbar.component';
import { MatSnackBar } from '@angular/material/snack-bar';


@Component({
  selector: 'table-overview-example',
  templateUrl: './table-playlist.component.html',
  styleUrls: ['./table-playlist.component.css']
})
export class TableOverviewExample implements AfterViewInit {
  // Declara el EventEmitter
  //@Output() editarClicked = new EventEmitter<boolean>(); 
  @Output() editarClicked = new EventEmitter<{editar: boolean, playlistId: string}>();



  // Esta función se llamará cuando se haga clic en el botón "Editar"
  
  /*onEditarClick() {
    this.editarClicked.emit(true); // emite true cuando se hace clic en el botón "Editar"
  }*/

  onEditarClick(playlistId: string) {
    this.editarClicked.emit({editar: true, playlistId: playlistId});
  }
  


  displayedColumns: string[] = ['playlist_id', 'name', 'descripcion', 'botones'];
  dataSource: MatTableDataSource<Playlist>;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(private playlistService: PlaylistService, private _snackBar: MatSnackBar) {
    this.dataSource = new MatTableDataSource<Playlist>([]);
  }

  durationInSeconds = 10;

  ngAfterViewInit() {
    this.playlistService.getPlaylists().subscribe({
      next: response => {
        if (response.status === 200 && response.body) {
          this.dataSource.data = response.body.playlists.map(playlist => ({
            playlist_id: playlist.playlist_id,
            name: playlist.name,
            descripcion: playlist.descripcion,
            botones: '',
          }));
        }
      },
      error: err => {
        // Aquí es donde manejas el error.
        console.error('Hubo un error al obtener las playlists:', err);
        this._snackBar.openFromComponent(CustomSnackbarComponent, {
          duration: this.durationInSeconds * 1000,
          horizontalPosition:"center",
          verticalPosition:"top",
          data:{ message:err},
          panelClass:['error-snackbar'],
        });
      },
      complete: () => {
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      }
    });
    
  }
  
  

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
}
