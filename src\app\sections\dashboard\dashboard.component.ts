import { Component } from '@angular/core';
import { RoutingService } from '../../services/routing-service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css'
})
export class DashboardComponent {
  currentRoute='';
  constructor(private routingService:RoutingService, private router:Router){
    this.router.events.subscribe((e) => {
      this.routingService.getNewRoute().subscribe((res) => {
        this.currentRoute = res;
      });
    });
  }
  getRoute(){
    return this.routingService.getNewRoute();
  }
  navigateTo(url:string){
    this.routingService.add(url);
  }
}
