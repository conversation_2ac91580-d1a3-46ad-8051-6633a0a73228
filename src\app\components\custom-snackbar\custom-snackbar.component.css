:host {
    display: flex;
    border-radius: 12px;
    width: 413px;
}

.custom-snackbar-label {
    color: white;
    font-family: "Visby CF";
    font-size: 16px;
    font-weight: 600;
    line-height: 25px;
    letter-spacing: 0em;
    text-align: center;
}

.custom-snackbar-icon {
    color: white;
    --mdc-icon-button-state-layer-size: 32px;
    --mdc-icon-button-icon-size: 24px;
    padding: 4px;
}

.custom-snackbar-icon ::ng-deep .mat-mdc-button-touch-target {
    width: var(--mdc-icon-button-state-layer-size);
    height: var(--mdc-icon-button-state-layer-size);
}