import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { SuscriptorsGroupedByPeriod, SuscriptorsResponse } from '../types/suscriptor';
import { CARRIERS } from '../types/api';

@Injectable({
    providedIn: 'root',
})
export class SuscriptorsService {
  constructor(private http:HttpClient){}

  private apiUrl = environment.apiUrl;
  private serviceUrl = `${this.apiUrl}/suscriptor`;

  public getSuscriptors(initDate:string,limitDate:string) {
    return this.http.get<SuscriptorsResponse>(`${this.serviceUrl}`,{ observe:"response", responseType:"json", params:{initDate,limitDate} },)
  }

  public getSuscriptorsHistory(periodos:{fecha:string}[],carrier:CARRIERS|null){
    return this.http.post<SuscriptorsGroupedByPeriod[]>(`${this.serviceUrl}/grouped`,{periodos,carrier},{ observe:"response", responseType:"json" },)
  }
}