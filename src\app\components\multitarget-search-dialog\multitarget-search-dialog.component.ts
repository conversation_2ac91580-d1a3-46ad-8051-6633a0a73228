import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { OptionType, SearchResults, asAlbum, asArtist, asOption, asSong } from '../../types/search';
import { Observable, debounceTime, distinctUntilChanged, filter, map, switchMap } from 'rxjs';
import { SearchService } from '../../services/search-service';

@Component({
  selector: 'app-multitarget-search-dialog',
  templateUrl: './multitarget-search-dialog.component.html',
  styleUrl: './multitarget-search-dialog.component.css'
})
export class MultitargetSearchDialogComponent implements OnInit{
  constructor(private searchService: SearchService) {}

  @Output() onOptionClick = new EventEmitter<asOption>(true);

  searchGroup = new FormGroup({
    searchControl: new FormControl<string | asSong | asAlbum | asArtist>('')
  });
  options:(asSong|asAlbum|asArtist)[] = [];
  searchOptions!: Observable<(asSong | asAlbum | asArtist)[]>;

  displayFn(song: asOption): string {
    return song && song.nombre ? song.nombre: '';
  }

  resetSearch() {
    this.searchGroup.controls.searchControl.setValue('');
  }

  public OptionTypeParse(ind:number) {
    return ['Artista','Canción','Albúm'][ind];
  }

  public getAlbumImg(option: asSong | asArtist | asAlbum) {
    if(option.as === OptionType.SONG){
      return (option as asSong)?.idalbum?.urlcaratula;
    }
    else if(option.as === OptionType.ALBUM){
      return (option as asAlbum).urlcaratula;
    }
    return null;
  }

  invokeAction(option: asOption) {
    this.onOptionClick.emit(option);
  }

  matchHiglight(name:string,search:string) {
    return name.replace(new RegExp(search,'i'),(m) => `<b>${m}</b>`);
  }

  searchByName(search: string): Observable<(asSong|asAlbum|asArtist)[]> {
    return this.searchService.searchByName(search).pipe(map(({body}) => {
      const { albums,artists,songs } = body as SearchResults;
      return [
        ...artists.map((e) => ({...e,as:OptionType.ARTIST})),
        ...albums.map((e) => ({...e,as:OptionType.ALBUM})),
        ...songs.map((e) => ({...e,as:OptionType.SONG}))
      ].map(({nombre,..._e}) =>  ({..._e,nombre,matchTag:this.matchHiglight(nombre,search)}))
      }
    ))
  }

  ngOnInit() {
    this.searchOptions = this.searchGroup.controls.searchControl.valueChanges.pipe(
      distinctUntilChanged(),
      filter((v) => (typeof v === 'string' && v.trim() !== '') || (typeof v === 'object' && v?.nombre !== undefined)),
      debounceTime(500),
      switchMap((value) => {
        const name = typeof value === 'string' ? value : value?.nombre;
        return this.searchByName(name?.trim() || '');
      })
    );
    this.searchOptions.subscribe((res) => {
      this.options = res;
    });
  }
}
