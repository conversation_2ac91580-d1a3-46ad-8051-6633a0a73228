@import './../../../components//custom-date-picker.css';

:host {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
}

.main-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-left: 85px;
}

.nav-card {
    border-radius: 12px;
    margin-top: 20px;
    width: fit-content;
    color: white;
    display: flex;
    align-items: center;
}

.back-button {
    font-family: Visby CF;
    font-size: 14px;
    font-weight: 700;
    line-height: 20px;
}

.elipse-dot {
    margin: 0px 8px;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: #FFFFFF80;
}

p {
    margin: 0px;
    color: white;
}

.album-info-card {
    border-radius: 12px;
    margin-top: 30px;
    display: flex;
    align-items: center;
    width: 78%;
}

.table-card {
    background-color: #f7f9fb;
    border-radius: 12px;
    margin-top: 20px;
}

.album-info-card-image {
    height: 166px;
    width: 166px;
    margin-right: 10px;
}

.song-title {
    font-family: Visby CF;
    font-size: 28px;
    font-weight: 700;
    line-height: 44px;
    letter-spacing: 0.05em;
    text-align: left;
    margin: 0px;
    color: white;
}

.album-header {
    font-family: "Visby CF";
    font-size: 12px;
    font-weight: 500;
    line-height: 19px;
    letter-spacing: 0.05em;
    text-align: left;
    margin: 0px;
    color: #ffffff99;
}

.group-description-text {
    font-family: "Visby CF";
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
    letter-spacing: 0.05em;
    text-align: left;
}

.data-container {
    width: 100%;
    margin-top: 32px;
    display: flex;
    flex: 1;
    padding-bottom: 48px;
}

.data-table-container {
    width: 82.5%;
}

.data-actions-container {
    width: 17.5%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
}

/**/
table ::ng-deep th {
    font-family: Visby CF;
    font-size: 12px;
    font-weight: 600;
    line-height: 20px;
    letter-spacing: 0em;
    text-align: left;
    color: #550dc5;
}

table ::ng-deep td {
    font-family: Visby CF;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0em;
    text-align: left;
    color: #201F28;
}

.custom-sort {
    width: 16px;
    height: 16px;
    margin-left: 6px;
}

.custom-sort-inverse {
    transform: rotate(180deg);
}

::ng-deep .table-settings-sort {
    display: none;
}


::ng-deep .mat-sort-header-arrow {
    display: none !important;
}

::ng-deep .mat-sort-header-container :last-child.ng-star-inserted {
    color: white;
}

.custom-date-picker {
    --mat-form-field-container-vertical-padding: 4px;
}

.reproductions-column {
    font-weight: 700;
    color: #df08a8;
}

@media screen and (min-width: 1440px) {
    .data-table-container {
        width: 78%;
    }

    .data-actions-container {
        width: 22%;
    }
}

.download-data-button {
    --mdc-fab-container-color: #DF08A8 !important;
    margin-bottom: -28px;
}

.reproductions-icon {
    margin-right: 7.5px;
    font-size: 18px;
    width: 18px;
    height: 18px;
}