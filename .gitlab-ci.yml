stages:
  - build
  - backup
  - deploy
  - test
  - rollback
  - error

build:
  stage: build
  before_script:
    - echo "Init Pipeline - Build Stage "
    - 'curl -X POST -H "Content-type: application/json" --data "{\"text\":\":large_yellow_circle: Dashboard Aqustico - Pipeline Stage - $CI_JOB_STAGE started :large_yellow_circle:\"}" *********************************************************************************'
    - echo $CI_COMMIT_REF_NAME
  script:
    - pwd
    - cd
    - pwd
    - git clone https://$GIT_USER:$GIT_PASS@$GITLAB_URL
    - ls
    - cd dashboard-aqustico-frontend
    - git checkout -f $CI_COMMIT_REF_NAME
    - git branch
    - git status
    - nvm use 18.16.1
    - npm install
    - ls
    - npm run build
    - cd dist/dashboard-aqustico-frontend
    - mv browser dashboard-aqustico
    - zip -r dashboard-aqustico.zip dashboard-aqustico
    - eval `ssh-agent`
    - scp -v dashboard-aqustico.zip $SSH_USER@$SSH_IP1:$PATH_DEPLOY
    - scp dashboard-aqustico.zip $SSH_USER@$SSH_IP2:$PATH_DEPLOY
    - echo "end of build stage"
  after_script:
    - cd
    - rm -rf dashboard-aqustico-frontend
    - rm -rf /home/<USER>/builds/c20453f7/0/cntm/dashboard-aqustico-frontend
    - rm -rf /home/<USER>/builds/c20453f7/0/cntm/dashboard-aqustico-frontend.tmp
    - 'curl -X POST -H "Content-type: application/json" --data "{\"text\":\":white_check_mark: Dashboard Aqustico - Pipeline Stage - $CI_JOB_STAGE finished :white_check_mark:\"}" *********************************************************************************'
  only:
    - master

backup:
  stage: backup
  before_script:
    - echo "Init Backup - Stage"
    - 'curl -X POST -H "Content-type: application/json" --data "{\"text\":\":large_yellow_circle: Dashboard Aqustico - Pipeline Stage - $CI_JOB_STAGE started :large_yellow_circle:\"}" *********************************************************************************'
  script:
    - VAR=$(date +%d%m%y | sed 's/\(:[0-9][0-9]\)[0-9]*$/\1/') && echo $VAR
    - ssh $SSH_USER@$SSH_IP1 "cd $PATH_DEPLOY; zip -r dashboard-aqustico_$VAR.zip dashboard-aqustico; mv dashboard-aqustico_$VAR.zip $PATH_BU; ls $PATH_BU; exit"
    - ssh $SSH_USER@$SSH_IP2 "cd $PATH_DEPLOY; zip -r dashboard-aqustico_$VAR.zip dashboard-aqustico; mv dashboard-aqustico_$VAR.zip $PATH_BU; ls $PATH_BU; exit"
    - echo "end of the backup stage"
  after_script:
    - 'curl -X POST -H "Content-type: application/json" --data "{\"text\":\":white_check_mark: Dashboard Aqustico - Pipeline Stage - $CI_JOB_STAGE finished :white_check_mark:\"}" *********************************************************************************'
  only:
    - master

deploy:
  stage: deploy
  before_script:
    - echo "Init Backup - Deploy"
    - 'curl -X POST -H "Content-type: application/json" --data "{\"text\":\":large_yellow_circle: Dashboard Aqustico - Pipeline Stage - $CI_JOB_STAGE started :large_yellow_circle:\"}" *********************************************************************************'
  script:
    - ssh $SSH_USER@$SSH_IP1 "cd $PATH_DEPLOY; rm -r dashboard-aqustico; unzip dashboard-aqustico.zip; ls; rm dashboard-aqustico.zip; exit"
    - ssh $SSH_USER@$SSH_IP2 "cd $PATH_DEPLOY; rm -r dashboard-aqustico; unzip dashboard-aqustico.zip; ls; rm dashboard-aqustico.zip; exit"
    - echo "end of the backup stage"
  after_script:
    - 'curl -X POST -H "Content-type: application/json" --data "{\"text\":\":white_check_mark: Dashboard Aqustico - Pipeline Stage - $CI_JOB_STAGE finished :white_check_mark:\"}" *********************************************************************************'
  only:
    - master

test:
  stage: test
  before_script:
    - echo "Init URL Test - Test"
    - 'curl -X POST -H "Content-type: application/json" --data "{\"text\":\":large_yellow_circle: Dashboard Aqustico - Pipeline Stage - $CI_JOB_STAGE started :large_yellow_circle:\"}" *********************************************************************************'
  script:
    - response=$(wget --spider -S "https://dashboard.aqustico.com/" 2>&1)
    - if echo "$response" | grep -q "200 OK"; then echo "Response is 200 OK"; else exit 1; fi
    - echo "end of the test stage"
  after_script:
    - 'curl -X POST -H "Content-type: application/json" --data "{\"text\":\":white_check_mark: Dashboard Aqustico - Pipeline Stage - $CI_JOB_STAGE finished :white_check_mark:\"}" *********************************************************************************'
  only:
    - master

rollback:
  stage: rollback
  before_script:
    - echo "Init Stage - Rollback"
    - 'curl -X POST -H "Content-type: application/json" --data "{\"text\":\":rotating_light::warning: Dashboard Aqustico - Pipeline Stage - $CI_JOB_STAGE started :rotating_light::warning:\"}" *********************************************************************************'
  script:
    - echo "Triggering rollback stage"
    - VAR=$(date +%d%m%y | sed 's/\(:[0-9][0-9]\)[0-9]*$/\1/') && echo $VAR
    - ssh $SSH_USER@$SSH_IP1 "mv $PATH_BU/dashboard-aqustico_$VAR.zip $PATH_DEPLOY; cd $PATH_DEPLOY; rm -r Dashboard Aqustico; unzip dashboard-aqustico_$VAR.zip; ls $PATH_DEPLOY; exit"
    - ssh $SSH_USER@$SSH_IP2 "mv $PATH_BU/dashboard-aqustico_$VAR.zip $PATH_DEPLOY; cd $PATH_DEPLOY; rm -r Dashboard Aqustico; unzip dashboard-aqustico_$VAR.zip; ls $PATH_DEPLOY; exit"
  after_script:
    - 'curl -X POST -H "Content-type: application/json" --data "{\"text\":\":white_check_mark: Dashboard Aqustico - Pipeline Stage - $CI_JOB_STAGE finished :white_check_mark:\"}" *********************************************************************************'
  only:
    - test
  when: on_failure

error:
  stage: error
  when: on_failure
  script:
    - 'curl -X POST -H "Content-type: application/json" --data "{\"text\":\":rotating_light: :rotating_light: Dashboard Aqustico - Pipeline Failed - $CI_COMMIT_REF_NAME :rotating_light: :rotating_light:\"}" *********************************************************************************'

