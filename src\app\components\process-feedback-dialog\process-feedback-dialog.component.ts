import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-process-feedback-dialog',
  templateUrl: './process-feedback-dialog.component.html',
  styleUrl: './process-feedback-dialog.component.css'
})
export class ProcessFeedbackDialogComponent {
  constructor( @Inject(MAT_DIALOG_DATA) public data: { message: string, icon:string, isPositive: boolean },
    private dialogRef: MatDialogRef<ProcessFeedbackDialogComponent>
  ) {}
}
