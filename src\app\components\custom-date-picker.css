.custom-date-picker {
    cursor: pointer;
    --as-border-color: #ffffff9e;
    --mdc-outlined-text-field-input-text-color: white;
    --mat-form-field-leading-icon-color: #ffffff9e;
    --mat-form-field-container-vertical-padding: 6px;
    --mat-form-field-container-height: 16px;
    --mat-form-field-container-text-weight: 700;
    --mat-form-field-container-text-size: 12px;
    --mat-form-field-container-text-line-height: 18px;
    --mdc-icon-button-icon-color: #FFFFFF9E;
    --mdc-outlined-text-field-hover-outline-color: var(--as-border-color);
    --mdc-outlined-text-field-outline-color: var(--as-border-color);
    --mdc-outlined-text-field-focus-outline-color: var(--as-border-color);
    --mdc-outlined-text-field-caret-color: white;
    --mdc-outlined-text-field-input-text-placeholder-color: white;
    --mdc-outlined-text-field-container-shape: 12px;
    width: 144px;
}

.custom-date-picker-icon {
    padding: 0px 2px 0px 8px !important;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.custom-date-picker-input {
    text-align: center;
}


.expand-datepicker:hover {
    width: 152px;
    transition: width 500ms ease-in-out;
}

.expand-datepicker-input {
    margin-left: 0px;
    transition: margin-left 1000ms ease-in-out;
}

.expand-datepicker-input:hover {
    margin-left: 4px;
    transition: margin-left 1000ms ease-in-out;
}

.custom-date-picker-toggle-icon {
    padding: 0px 8px 0px 2px !important;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.custom-date-picker-toggle-icon ::ng-deep .mat-mdc-icon-button {
    --mdc-icon-button-state-layer-size: 16px;
    --mdc-icon-button-icon-size: 16px;
    padding: 0px;
}

.custom-date-picker-toggle-icon ::ng-deep .mat-mdc-button-touch-target {
    width: var(--mdc-icon-button-state-layer-size);
    height: var(--mdc-icon-button-state-layer-size);
}

.custom-date-picker-header {
    font-family: "Visby CF";
    font-size: 10px;
    font-weight: 600;
    line-height: 18px;
    letter-spacing: 0em;
    text-align: left;
    color: white;
    margin-bottom: 3px;
}

.custom-date-picker-header:has(+.expand-datepicker:hover) {
    margin-left: 8px;
    transition: margin-left 500ms ease-in-out;
}

.custom-date-picker-apply-btn {
    color: white !important;
    background-color: #550dc5 !important;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 700;
    font-family: "Visby CF";
    line-height: 22px;
}

::ng-deep .mat-datepicker-content:has(::ng-deep .menu-backdrop) {
    border-radius: 12px;
    padding: 22.5px 39.5px;
    --mat-datepicker-calendar-container-text-color: transparent;
    margin-top: 10px;
}

::ng-deep .mat-calendar-content ::ng-deep button {
    font-weight: 600;
}

::ng-deep .mat-calendar-table-header-divider {
    display: none;
}

::ng-deep .menu-backdrop {
    --mat-menu-container-shape: 12px;
    --mat-datepicker-calendar-date-selected-state-background-color: #550DC5;
    --mat-datepicker-calendar-header-text-color: #550DC5;
    --mat-datepicker-calendar-header-text-weight: 600;
    --mat-datepicker-calendar-header-text-size: 15px;
    --mat-datepicker-calendar-text-font: "Visby CF";
    margin-top: 10px;
}

::ng-deep .menu-backdrop-shadow {
    background-color: rgba(0, 0, 0, 0.25) !important;
}