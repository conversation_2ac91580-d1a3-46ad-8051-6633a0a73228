import { HttpClient,  HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

// Definición de la interfaz Playlist
export interface Playlist {
  playlist_id: number;
  name: string;
  descripcion: string;
  botones?: string;  // Agrega la propiedad botones
}

// Definición de constantes para los códigos de estado HTTP
const HTTP_STATUS_BAD_REQUEST = 400;
const HTTP_STATUS_UNAUTHORIZED = 401;
const HTTP_STATUS_FORBIDDEN = 403;
const HTTP_STATUS_NOT_FOUND = 404;
const HTTP_STATUS_INTERNAL_SERVER_ERROR = 500;

@Injectable({
  providedIn: 'root'
})
export class PlaylistService {
    
  private url = `${environment.apiUrl}/playlist`;

  constructor(private http: HttpClient) { }

  // Uso de la interfaz Playlist para definir el tipo de retorno
  getPlaylists(): Observable<HttpResponse<{playlists: Playlist[]}>> {
    return this.http.get<{playlists: Playlist[]}>(this.url, { observe: 'response' }).pipe(
        tap(response => {
            if (response.status === 200) {
              console.log('Exito obteniendo las playlists del servidor:', response.body);
            }
        }),
        catchError(error => {
            let errorMessage = 'Ha ocurrido un error desconocido';
          
            if (error.error instanceof ErrorEvent) {
              // Errores del lado del cliente o de la red.
              errorMessage = `Error: ${error.error.message}`;
            } else {
              // Errores del lado del servidor.
              switch (error.status) {
                case HTTP_STATUS_BAD_REQUEST:
                  errorMessage = 'Solicitud incorrecta. Por favor, verifica los datos del archivo.';
                  break;
                case HTTP_STATUS_UNAUTHORIZED:
                  errorMessage = 'No autorizado. Por favor, verifica tus credenciales.';
                  break;
                case HTTP_STATUS_FORBIDDEN:
                  errorMessage = 'Prohibido. No tienes permiso para realizar esta acción.';
                  break;
                case HTTP_STATUS_NOT_FOUND:
                  errorMessage = 'No se encontró el recurso solicitado.';
                  break;
                case HTTP_STATUS_INTERNAL_SERVER_ERROR:
                  errorMessage = 'Error interno del servidor. Por favor, intenta más tarde.';
                  break;
                default:
                  errorMessage = `Error del servidor: código ${error.status}, mensaje: ${error.message}`;
                  break;
              }
            }
          
            console.error(errorMessage);
            return throwError(() => errorMessage);
         })
    );
  }
}
