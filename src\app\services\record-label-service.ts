import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { RecordLabelSearchParams } from '../types/record-label';

@Injectable({
    providedIn: 'root',
})
export class RecordLabelService {
    private apiUrl = environment.apiUrl;
    private serviceUrl = `${this.apiUrl}/record-label`;

    constructor(private http: HttpClient) {}

    getRecordLabelReproductions(searchParams: RecordLabelSearchParams): Observable<any> {
        const nParams = new HttpParams({fromObject:(searchParams as { [p:string]:any})});
        return this.http.get<any>(this.serviceUrl,{params: nParams,observe: 'response'},);
    }
}