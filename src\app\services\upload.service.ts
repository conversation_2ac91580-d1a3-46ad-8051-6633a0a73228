import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../environments/environment.development';

@Injectable({
  providedIn: 'root'
})
export class UploadService {

  private url = `${environment.apiUrl}/song`;

  constructor(private http: HttpClient) { }

  uploadFile(file: File) {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post(this.url, formData, {
      headers: {
        'accept': '*/*'
      },
      observe: 'response'
    }).pipe(
      map(response => {
        // Aquí puedes manejar la respuesta
        if (response.status === 200) {
          console.log('Exito enviando canciones al servidor:', response.body);
        }
        return response.body;
      }),
      catchError(error => {
        let errorMessage = 'Ha ocurrido un error desconocido';
      
        if (error.error instanceof ErrorEvent) {
          // Errores del lado del cliente o de la red.
          errorMessage = `Error: ${error.error.message}`;
        } else {
          // Errores del lado del servidor.
          switch (error.status) {
            case 400:
              errorMessage = 'Solicitud incorrecta. Por favor, verifica los datos del archivo.';
              break;
            case 401:
              errorMessage = 'No autorizado. Por favor, verifica tus credenciales.';
              break;
            case 403:
              errorMessage = 'Prohibido. No tienes permiso para realizar esta acción.';
              break;
            case 404:
              errorMessage = 'No se encontró el recurso solicitado.';
              break;
            case 500:
              errorMessage = 'Error interno del servidor. Por favor, intenta más tarde.';
              break;
            default:
              errorMessage = `Error del servidor: código ${error.status}`;
              break;
          }
        }
      
        console.error(errorMessage);
        return throwError(() => errorMessage);
      })   
    );
  }
}
