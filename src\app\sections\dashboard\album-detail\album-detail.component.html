<div class="main-container">
  <div class="nav-card" (click)="invokeNavigateTo('dashboard/reproductions/')" style="cursor: pointer;">
    <mat-icon style="width: 16px;height: 16px;font-size: 16px;margin-right: 12px;">keyboard_arrow_left </mat-icon>
    <p class="back-button">Regresar a reproducciones</p>
  </div>
  <div *ngIf="_albumData  !== undefined" class="album-info-card">
    <div style="flex: 1; display: flex; align-items: center; height: 100%">
      <img class="album-info-card-image" [src]="_albumData.urlcaratula" />
      <div>
        <h5 class="album-header">ÁLBUM</h5>
        <h3 class="song-title">{{ _albumData.nombre }}</h3>
        <div style="display: flex; align-items: center" class="group-description-text">
          <p>{{ _albumData.idartista.nombre }}</p>
          <div class="elipse-dot"></div>
          <p>{{ parseDecade(_albumData.fechacreacion,'YYYY') }}</p>
          <div class="elipse-dot"></div>
          <p>{{ _albumData.songs.length }} {{ _albumData.songs.length > 1 ? 'canciones':'canción'}}</p>
        </div>
      </div>
    </div>
    <div style="display: flex;gap: 17px;">
      <div style="display: flex; flex-direction: column">
        <p class="custom-date-picker-header">Desde</p>
        <mat-form-field floatLabel="always" subscriptSizing="dynamic" appearance="outline"
          class="custom-date-picker expand-datepicker">
          <input matInput [matDatepicker]="datepicker" [formControl]="reproductionsSearchForm.controls.initDate"
            readonly
            [max]="reproductionsSearchForm.controls.limitDate.value !== null?reproductionsSearchForm.controls.limitDate.value:maxDate"
            [disabled]="true" class="custom-date-picker-input" />
          <mat-icon matPrefix class="custom-date-picker-icon">event</mat-icon>
          <mat-datepicker-toggle matIconSuffix [for]="datepicker" class="custom-date-picker-toggle-icon">
            <mat-icon matDatepickerToggleIcon class="custom-date-picker-toggle-icon">
              keyboard_arrow_down
            </mat-icon>
          </mat-datepicker-toggle>
          <mat-datepicker panelClass="menu-backdrop" #datepicker [calendarHeaderComponent]="customHeader">
            <mat-datepicker-actions>
              <button mat-button matDatepickerCancel style="border-radius: 12px">
                Cancelar
              </button>
              <button mat-raised-button matDatepickerApply class="custom-date-picker-apply-btn">
                Aceptar
              </button>
            </mat-datepicker-actions>
          </mat-datepicker>
        </mat-form-field>
      </div>
      <div style="display: flex; flex-direction: column">
        <p class="custom-date-picker-header">Hasta</p>
        <mat-form-field floatLabel="always" subscriptSizing="dynamic" appearance="outline"
          class="custom-date-picker expand-margin-datepicker">
          <input matInput [matDatepicker]="datepickerv" [formControl]="reproductionsSearchForm.controls.limitDate"
            readonly [min]="reproductionsSearchForm.controls.initDate.value" [max]="maxDate"
            placeholder=" -- / -- / -- " class="custom-date-picker-input expand-datepicker-input"
            [style]="reproductionsSearchForm.controls.limitDate.value === null ? 'letter-spacing: 2.5px;':''" />
          <mat-icon matPrefix class="custom-date-picker-icon">event</mat-icon>
          <mat-datepicker-toggle matIconSuffix [for]="datepickerv" class="custom-date-picker-toggle-icon">
            <mat-icon matDatepickerToggleIcon class="custom-date-picker-toggle-icon">keyboard_arrow_down
            </mat-icon>
          </mat-datepicker-toggle>
          <mat-datepicker panelClass="menu-backdrop" #datepickerv disabled="false"
            [calendarHeaderComponent]="customHeader">
            <mat-datepicker-actions>
              <button mat-button matDatepickerCancel>Cancelar</button>
              <button mat-raised-button color="primary" matDatepickerApply class="custom-date-picker-apply-btn">
                Aceptar
              </button>
            </mat-datepicker-actions>
          </mat-datepicker>
        </mat-form-field>
      </div>
    </div>
  </div>
  <div class="data-container">
    <div class="data-table-container">
      <mat-card style="background-color: #f7f9fb; border-radius: 12px">
        <table mat-table [dataSource]="table.dataSource" matMultiSort (matSortChange)="table.onSortEvent()"
          [style]="_albumData  !== undefined?'background-color:var(--table-background-color);border-radius: 12px;':'display:none;'">
          <ng-container matColumnDef="ind">
            <th mat-header-cell *matHeaderCellDef width="fit-content"></th>
            <td mat-cell *matCellDef="let row = index" style="width: 16px;padding: 0px 8px 0px 16px;">{{(row+1)+'.'}}</td>
          </ng-container>
          <ng-container matColumnDef="song">
            <th mat-header-cell *matHeaderCellDef mat-multi-sort-header="song">
              Tema
              <mat-icon *ngIf="isSortingActive('song')" svgIcon="chevron_icon"
                [class]="getSortingDirection('song') === 'asc' ? 'custom-sort custom-sort-inverse':'custom-sort'"></mat-icon>
            </th>
            <td mat-cell *matCellDef="let row">{{ row.song }}</td>
          </ng-container>
          <ng-container matColumnDef="duration">
            <th mat-header-cell *matHeaderCellDef mat-multi-sort-header="duration">
              Duración
              <mat-icon *ngIf="isSortingActive('duration')" svgIcon="chevron_icon"
                [class]="getSortingDirection('duration') === 'asc' ? 'custom-sort custom-sort-inverse':'custom-sort'"></mat-icon>
            </th>
            <td mat-cell *matCellDef="let row">{{ parseDuration(row.duration) }}</td>
          </ng-container>
          <ng-container matColumnDef="reproductions">
            <th mat-header-cell *matHeaderCellDef mat-multi-sort-header="reproductions">
              Reproducciones
              <mat-icon *ngIf="isSortingActive('reproductions')" svgIcon="chevron_icon"
                [class]="getSortingDirection('reproductions') === 'asc' ? 'custom-sort custom-sort-inverse':'custom-sort'"></mat-icon>
            </th>
            <td mat-cell *matCellDef="let row" class="reproductions-column">
              <div style="display: flex; align-items: center">
                <mat-icon svgIcon="reproductions_icon" class="reproductions-icon"></mat-icon>
                <span style="margin-right: 25px">
                  {{ quantityParser(row.reproductions) }}
                </span>
                <mat-icon [svgIcon]="row.tendency === 1? 'up_tendency_icon': 'down_tendency_icon'" style="width: 16px;height: 16px;"></mat-icon>
              </div>
            </td>
          </ng-container>
          <tr mat-header-row *matHeaderRowDef="table.displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: table.displayedColumns;"></tr>
        </table>
      </mat-card>
    </div>
    <div class="data-actions-container">
      <button *ngIf="_albumData  !== undefined" mat-fab color="accent" (click)="exportData()" class="download-data-button">
        <mat-icon svgIcon="download_icon"></mat-icon>
      </button>
    </div>
  </div>
</div>