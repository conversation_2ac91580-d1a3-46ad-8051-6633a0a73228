import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RadioFinishProcessDialogComponent } from './radio-finish-process-dialog.component';

describe('RadioFinishProcessDialogComponent', () => {
  let component: RadioFinishProcessDialogComponent;
  let fixture: ComponentFixture<RadioFinishProcessDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RadioFinishProcessDialogComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(RadioFinishProcessDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
