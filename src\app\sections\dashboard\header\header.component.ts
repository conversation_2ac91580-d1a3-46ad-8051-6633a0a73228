import { Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { OptionType, SearchResults, asAlbum, asArtist, asOption, asSong } from '../../../types/search';
import { Observable, map, startWith } from 'rxjs';
import { SearchService } from '../../../services/search-service';
import { MultitargetSearchDialogComponent } from '../../../components/multitarget-search-dialog/multitarget-search-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { UserProfileService } from '../../../services/user-profile-service';
import { Roles } from '../../../types/auth';
interface RouteElementType {
  asName: string;
  isAbsolute: boolean;
  absoluteIcon?: string;
  children: { [key: string]: RouteElementType };
}

@Component({
  selector: "app-header",
  templateUrl: "./header.component.html",
  styleUrl: "./header.component.css",
})
export class HeaderComponent implements OnChanges, OnInit {
  cargacancion: string = "";
  @Input() currentRoute = "";
  @Output("navigateTo") navigateTo: EventEmitter<any> = new EventEmitter();
  @ViewChild("searchDisplayButton", { static: false })
  public dialogDisplayButtonRef!: ElementRef<HTMLButtonElement>;
  breadcrumbElements: Omit<RouteElementType,"children">[] = [];
  activateCustomSearch = false;
  rootIcon: string | undefined = undefined;
  isSpecialSectionActive = false;
  constructor(
    private searchService: SearchService,
    private readonly userProfileService: UserProfileService,
    private searchDialog: MatDialog
  ) {}
  headerFormGroup = new FormGroup({
    searchControl: new FormControl<string | asSong | asAlbum | asArtist>(""),
  });

  options: (asSong | asAlbum | asArtist)[] = [];
  searchOptions!: Observable<(asSong | asAlbum | asArtist)[]>;

  invokeNavigateTo(url: string) {
    this.navigateTo.emit(url);
  }

  openSearchDialog() {
    const btnRef: DOMRect =
      this.dialogDisplayButtonRef.nativeElement.getBoundingClientRect();
    const dialogRef = this.searchDialog.open(MultitargetSearchDialogComponent, {
      hasBackdrop: true,
      position: {
        top: `${btnRef.top + btnRef.height + 15}px`,
        left: `${btnRef.left}px`,
      },
      backdropClass: "non-opacity-background",
    });
    const sub = dialogRef.componentInstance.onOptionClick.subscribe(
      (data: asOption) => {
        dialogRef.close();
        this.searchRedirect(data);
      }
    );
  }

  public OptionTypeParse(ind: number) {
    return ["Artista", "Canción", "Albúm"][ind];
  }

  private _filter(name: string): (asSong | asAlbum | asArtist)[] {
    const filterValue = name.toLowerCase();
    return this.options.filter((option) =>
      option.nombre.toLowerCase().includes(filterValue)
    );
  }
  
  routeMap: {
    [route: string]: {
      asName: string;
      isAbsolute: boolean;
      absoluteIcon?: string;
    };
  } = {
    dashboard: { asName: "Dashboards", isAbsolute: false },
    overview: { asName: "Overview", isAbsolute: false },
    reproductions: { asName: "Reproducciones", isAbsolute: false },
    "album-detail": { asName: "Detalle Álbum", isAbsolute: false },
    "carga-canciones": { asName: "Carga canciones", isAbsolute: false },
    "play-list": { asName: "Playlist", isAbsolute: false },
    "banner-list": { asName: "Banners", isAbsolute: true },
    radio: { asName: "Radios", isAbsolute: true, absoluteIcon: "radio" },
  };

  parseRouteV2(
    rawRoute: string[],
    routeMap: RouteElementType,
    accPath: Omit<RouteElementType,"children">[]
  ): Omit<RouteElementType,"children">[] {
    if (rawRoute.length >= 1) {
      const nextRoute = rawRoute.slice(1);
      if (nextRoute.length > 0 && routeMap.children[nextRoute[0]]) {
        return this.parseRouteV2(nextRoute, routeMap.children[nextRoute[0]], [
          ...accPath,
          {asName: routeMap.asName, isAbsolute: routeMap.isAbsolute, absoluteIcon: routeMap.absoluteIcon},
        ]);
      } 
      else {
        return [...accPath, {asName: routeMap.asName, isAbsolute: routeMap.isAbsolute, absoluteIcon: routeMap.absoluteIcon}];
      }
    }
    return accPath;
  }

  bRoute: RouteElementType = {
    asName: "Dashboard",
    isAbsolute: false,
    children: {
      overview: {
        asName: "Overview",
        isAbsolute: false,
        children: {},
      },
      "play-list": {
        asName: "Playlists",
        isAbsolute: true,
        children: {},
      },
      reproductions: {
        asName: "Reproducciones",
        isAbsolute: false,
        children: {
          "album-detail": {
            asName: "Detalle de Álbum",
            isAbsolute: false,
            children: {},
          },
        },
      },
      "banner-list": {
        asName: "Banners",
        isAbsolute: true,
        children: {
          'new': {
            asName: "Crear Banner",
            isAbsolute: true,
            children: {},
          },
          'update': {
            asName: "Actualizar Banner",
            isAbsolute: true,
            children: {},
          },
        },
      },
      "carga-canciones": {
        asName: "Carga canciones",
        isAbsolute: false,
        children: {},
      },
      radio: {
        asName: "Radios",
        isAbsolute: true,
        absoluteIcon: "radio",
        children: {},
      },
    },
  };

  searchRedirect(option: asOption) {
    this.searchService.addQuickSearch(option.nombre);
    this.invokeNavigateTo("dashboard/reproductions");
  }

  parseRoute(): Omit<RouteElementType,"children">[] {
    const splitRoute = this.currentRoute.split("/").slice(1);

    return this.parseRouteV2(splitRoute, this.bRoute, [])
  }

  displayFn(song: asOption): string {
    return song && song.nombre ? song.nombre : "";
  }

  public getAlbumImg(option: asSong | asArtist | asAlbum) {
    if (option.as === OptionType.SONG) {
      return (option as asSong)?.idalbum?.urlcaratula;
    } else if (option.as === OptionType.ALBUM) {
      return (option as asAlbum).urlcaratula;
    }
    return null;
  }

  searchByName(name: string) {
    if (name !== "") {
      this.searchService.searchByName(name).subscribe({
        next: (res) => {
          const { albums, artists, songs } = res.body as SearchResults;
          this.options = [
            ...artists.map((e) => ({ ...e, as: OptionType.ARTIST })),
            ...songs.map((e) => ({ ...e, as: OptionType.SONG })),
            ...albums.map((e) => ({ ...e, as: OptionType.ALBUM })),
          ] as asOption[];
        },
        error: (err) => {
          console.log(err);
        },
      });
    }
  }

  ngOnInit(): void {
    const userProfile = this.userProfileService.getProfileSync();
    if ( userProfile && [Roles.ARTIST,Roles.RECORD_LABEL].includes(userProfile.role)) {
      this.isSpecialSectionActive = true;
    }
    this.searchOptions =
      this.headerFormGroup.controls.searchControl.valueChanges.pipe(
        startWith(""),
        map((value) => {
          const name = typeof value === "string" ? value : value?.nombre;
          return name ? this._filter(name as string) : this.options.slice();
        })
      );
    this.headerFormGroup.controls.searchControl.valueChanges.subscribe(
      (value) => {
        if (typeof value === "string") {
          this.searchByName(value as string);
        }
      }
    );
  }

  ngOnChanges(changes: SimpleChanges): void {
    const nRoute = this.parseRoute();
    const userProfile = this.userProfileService.getProfileSync();
    if(nRoute.find((e) => e.isAbsolute)) {
      this.breadcrumbElements = nRoute.filter((e) => e.isAbsolute);
    } else {
      this.breadcrumbElements = nRoute;
    }
    if(userProfile?.role === Roles.RECORD_LABEL) {
      nRoute[0] = {asName:`Bienvenido, ${userProfile.record_label.name}`, isAbsolute: false, absoluteIcon: ''};
    }
    this.activateCustomSearch =
      this.breadcrumbElements[this.breadcrumbElements.length - 1].asName === "Overview"
        ? true
        : false;
  }
}
