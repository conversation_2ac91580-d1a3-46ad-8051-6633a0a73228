import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { User } from '../types/auth';
import { jwtCookie<PERSON>ey } from '../../constants/jwt';
import { CookieService } from 'ngx-cookie-service';

@Injectable({
    providedIn: 'root',
})
export class UserProfileService {
  constructor(private cookieService:CookieService){}
  profile!: User | undefined;
  private readonly user_profile_item:string ='D_AQS_PROFILE';

  addProfile(profileData:User,asToken:string) {
    localStorage.setItem(this.user_profile_item,JSON.stringify(profileData));
    localStorage.setItem(jwtCookieKey,asToken);
    this.cookieService.set(jwtCookieKey,asToken,undefined);
    this.profile = profileData;
  }
  logout() {
    localStorage.removeItem(this.user_profile_item);
    localStorage.removeItem(jwt<PERSON>ookie<PERSON><PERSON>);
    this.cookieService.delete(jwtCookie<PERSON>ey);
    this.profile = undefined;
  } 
  getProfile() : Observable<User | undefined> {
    if(this.profile === undefined && localStorage.getItem(this.user_profile_item) !== null){
      this.profile = JSON.parse(localStorage.getItem(this.user_profile_item) as string);
    }
    return of(this.profile);
  }

  getProfileSync() : User | undefined {
    return this.profile;
  }
}