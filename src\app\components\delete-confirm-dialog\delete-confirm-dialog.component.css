@import '../../shared/styles/common-dialog-layout.css';

::ng-deep .delete-confirm-dialog-container {
    --mdc-dialog-container-shape: 12px;
    --mat-dialog-container-max-width: 722px;
    --mat-dialog-container-min-width: 722px;
    --mat-dialog-with-actions-content-padding: 40px 56px 0px 56px;
    --mat-dialog-actions-padding: 0px 56px 56px 56px;
    --mdc-dialog-container-color: #1A191F;
}

.common-flat-button {
    --mdc-filled-button-label-text-font:"Visby CF";
    --mdc-filled-button-label-text-size:16px;
    --mdc-filled-button-label-text-weight:700;
    --mdc-filled-button-container-height:40px;
    --mdc-filled-button-container-shape:12px;
    --mat-filled-button-horizontal-padding: 32px;
    --mdc-filled-button-label-text-color:#1A191F;
    --mdc-filled-button-container-color:#1BE2ED;
    --mdc-filled-button-label-text-tracking:1px;

    &.plain {
        --mdc-filled-button-container-color:#E0E2E7;
    }

    &.delete-button {
        --mdc-filled-button-container-color:#00E5EF;
        --mdc-filled-button-label-text-color:#FFFFFF;
    }
}

.common-flat-button-disabled {
    &:disabled {
        --mdc-filled-button-disabled-label-text-color:#1A191F;
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.60) 0%, rgba(0, 0, 0, 0.60) 100%), var(--Aqua, #1BE2ED);
    }
}