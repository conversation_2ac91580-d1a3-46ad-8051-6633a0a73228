:host {
    display: flex;
    min-width: fit-content;
    box-sizing: border-box;
}

.image-pick-field {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    padding: 6px;
    border-radius: 8px;
    transition: background 0.5s ease-in-out;

    .image-pick-preview {
        border-radius: 8px;
        padding: 38px 39px;
        background: linear-gradient(137deg, #1C1468 1.26%, #3728CE 69.5%);
        width: 162px;
        max-width: 240px;
        min-height: 84px;
        max-height: 160px;
        transition: background 0.5s ease-in-out;
        position: relative;
        display: flex;

        mat-icon {
            margin: auto;
            color: #FFF;
            z-index: 10;
            font-size: 26.67px;
            height: 26.67px;
            width: 26.67px;
            position: absolute;
            left: calc(50% - 12px);
            top: calc(50% - 12px);
            transition: opacity 0.5s ease-in-out;
        }

        img {
            z-index: 10;
            position: absolute;
            top: 0px;
            left: 0px;
            width: 100%;
            height: 100%;
            border-radius: inherit;
        }

        &::before {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            right: 0;
            content: "";
            border-radius: inherit;
            position: absolute;
            background: linear-gradient(0deg, rgba(0, 0, 0, 0.52) 0%, rgba(0, 0, 0, 0.52) 100%), linear-gradient(137deg, #1C1468 1.26%, #3728CE 69.5%);
            opacity: 0;
            transition: opacity 0.5s ease;
        }
    }

    h5,
    h6 {
        color: #FFF;
        text-align: center;
        font-family: "Visby CF";
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        margin: 0px;
    }

    h6 {
        font-size: 14px !important;
        font-weight: 500 !important;
    }

    &:hover {
        background: rgba(255, 255, 255, 0.10);

        .image-pick-preview {
            &::before {
                opacity: 1;
            }
        }
    }

    &:hover> ::ng-deep .image-pick-preview {
        &::before {
            opacity: 1;
        }
    }
}
