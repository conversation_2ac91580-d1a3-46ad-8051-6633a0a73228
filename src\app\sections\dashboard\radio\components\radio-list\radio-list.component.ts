import { AfterViewInit, Component, OnInit, ViewChild } from "@angular/core";
import { MatPaginator, PageEvent } from "@angular/material/paginator";
import { MatSort } from "@angular/material/sort";
import { MatTableDataSource } from "@angular/material/table";
import { RadioService } from "../../../../../services/radio-service";
import { RadioType } from "../../../../../types/radio";
import { RoutingService } from "../../../../../services/routing-service";
import { map } from "rxjs";
import { MatDialog } from "@angular/material/dialog";
import { PublishRadioConfirmDialogComponent } from "../publish-radio-confirm-dialog/publish-radio-confirm-dialog.component";

@Component({
  selector: "app-radio-list",
  templateUrl: "./radio-list.component.html",
  styleUrl: "./radio-list.component.css",
})
export class RadioListComponent implements OnInit, AfterViewInit {
  displayedColumns: string[] = ["id", "title", "author", "actions"];
  dataSource: MatTableDataSource<RadioType>;
  pageSizeOptions: number[] = [10, 50, 100];
  pageSize: number = this.pageSizeOptions[0];
  pageIndex: number = 0;
  totalElements: number = 0;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private readonly _dialog: MatDialog,
    private readonly routingService: RoutingService,
    private readonly radioService: RadioService
  ) {
    this.dataSource = new MatTableDataSource<RadioType>([]);
  }

  onNewRadioClick() {
    this.routingService.add("/dashboard/radio/new");
  }

  onEditRadioClick(radio_id: number) {
    this.routingService.add(`/dashboard/radio/update/${radio_id}`);
  }

  refreshTableData(data:RadioType[]) {
    this.dataSource.data = data;
    this.totalElements = data.length;
  }

  onPageChange(e: PageEvent) {
    this.totalElements = e.length;
    this.pageIndex = e.pageIndex;
    this.pageSize = e.pageSize;
  }

  onPublishRadioClick(radio: RadioType) {
    let dRef = this._dialog.open(PublishRadioConfirmDialogComponent, {
      data:radio,
      disableClose: true,
      panelClass: "publish-radio-dialog-container",
    });
    dRef.afterClosed().subscribe((result) => {
      if (result) {
        this.getRadioList();
      }
    });
  }

  getRadioList() {
    this.radioService
      .getRadios()
      .pipe(map((response) => response.body as RadioType[]))
      .subscribe({
        next: (data) => {
          this.refreshTableData(data);
        },
        error: () => {},
      });
  }  

  ngOnInit(): void {
    this.getRadioList();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }
}
