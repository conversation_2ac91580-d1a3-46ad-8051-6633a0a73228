import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  ElementRef,
  OnInit,
  ViewChild,
} from "@angular/core";
import {
  MatMultiSort,
  MatMultiSortTableDataSource,
  TableData,
} from "ngx-mat-multi-sort";
import { Song, SongPlayback } from "../../../types/reproductions";
import { download, generateCsv, mkConfig } from "export-to-csv";
import { MatDialog } from "@angular/material/dialog";
import { RoutingService } from "../../../services/routing-service";
import moment, { Moment } from "moment";
import { PageEvent } from "@angular/material/paginator";
import { abbreviateNumber } from "js-abbreviation-number";
import { FormControl, FormGroup } from "@angular/forms";
import { CustomCalendarHeaderComponent } from "../../../components/custom-calendar-header/custom-calendar-header.component";
import { provideMomentDateAdapter } from "@angular/material-moment-adapter";
import { asOption } from "../../../types/search";
import { MultitargetSearchDialogComponent } from "../../../components/multitarget-search-dialog/multitarget-search-dialog.component";
import { debounceTime } from "rxjs";
import { ArtistReproductions, ArtistSearchParams } from "../../../types/artist";
import { ArtistService } from "../../../services/artist-service";

@Component({
  selector: 'app-artist-reproductions',
  templateUrl: './artist-reproductions.component.html',
  styleUrl: './artist-reproductions.component.css',
  providers: [
    provideMomentDateAdapter({
      parse: {
        dateInput: ["l", "LL"],
      },
      display: {
        dateInput: "DD / MM / YYYY",
        monthYearLabel: "MMM YYYY",
        dateA11yLabel: "LL",
        monthYearA11yLabel: "MMMM YYYY",
      },
    }),
  ]
})
export class ArtistReproductionsComponent {
  @ViewChild("searchDisplayButton", { static: false })
  public dialogDisplayButtonRef!: ElementRef<HTMLButtonElement>;
  @ViewChild(MatMultiSort) sort!: MatMultiSort;
  maxDate: Moment;
  usedFilters: string[] = [];
  csvConfig = mkConfig({
    columnHeaders: [
      { key: "song", displayLabel: "Tema" },
      { key: "albumname", displayLabel: "Albúm" },
      { key: "genre", displayLabel: "Género" },
      { key: "recordlabel", displayLabel: "Disquera" },
      { key: "reproductions", displayLabel: "Reproducciones" },
    ],
  });
  private formatter = new Intl.NumberFormat("en-US");
  pageSizes = [10, 20,50];

  baseData!: SongPlayback[];
  table: TableData<SongPlayback>;
  customHeader = CustomCalendarHeaderComponent;

  reproductionsSearchForm = new FormGroup({
    initDate: new FormControl<Moment>(
      moment().subtract(1, "d").subtract(1, "month")
    ),
    limitDate: new FormControl<Moment | null>(null),
  });

  constructor(
    private routingService: RoutingService,
    private artistService: ArtistService,
    private changeDetectorRef: ChangeDetectorRef,
    private searchDialog: MatDialog
  ) {
    this.maxDate = moment().subtract(1, "d");
    this.table = new TableData<SongPlayback>(
      [
        { id: "song", name: "Canción" },
        { id: "albumname", name: "Albúm" },
        { id: "genre", name: "Género" },
        { id: "recordlabel", name: "Disquera" },
        { id: "reproductions", name: "Reproducciones" },
      ],
      { localStorageKey: "settings" }
    );
  }

  invokeNavigateTo(url: string) {
    this.routingService.add(url);
  }

  public parseDecade(d: string, asFormat: string) {
    return moment(d).format(asFormat);
  }

  isSortingActive(sort: string): boolean {
    return this.table.sortParams.includes(sort);
  }
  getSortingDirection(sort: string): string {
    return this.table.sortDirs[
      this.table.sortParams.findIndex((e) => sort === e)
    ];
  }

  quantityParser(qty: string) {
    return abbreviateNumber(parseInt(qty), 1);
  }

  qtyFormat(target: number) {
    return this.formatter.format(target);
  }

  refreshTableData(statistics: ArtistReproductions) {
    this.table.totalElements = statistics.meta.totalItems;
    this.baseData = statistics.items;
    this.table.data = this.sortTableData(
      this.baseData,
      this.table.sortParams,
      this.table.sortDirs
    );
  }

  getSearchParams(): ArtistSearchParams {
    return {
      page: this.table.pageIndex ? this.table.pageIndex : 0,
      take: this.table.pageSize ? this.table.pageSize : this.pageSizes[0],
      startDate:
        this.reproductionsSearchForm.controls.initDate.value?.format(
          "YYYY-MM-DD"
        ),
      endDate:
        this.reproductionsSearchForm.controls.limitDate.value === null
          ? moment().subtract(1, "d").format("YYYY-MM-DD")
          : this.reproductionsSearchForm.controls.limitDate.value.format(
              "YYYY-MM-DD"
            ),
    };
  }

  exportData() {
      const {...searchParams} = this.getSearchParams();
      this.artistService.getArtistReproductions({...searchParams,page:0,take:this.table.totalElements}).subscribe({
        next: (res) => {
          const { items:songs } = (res.body as ArtistReproductions);
          const asData: any = this.sortTableData(songs,this.table.sortParams,this.table.sortDirs).map(({date,...r}) => ({...r,date:this.parseDecade(date,'YYYY')}));
          download({...this.csvConfig,filename:`REPRODUCCIONES_AQUSTICO_ARTISTA`})(generateCsv(this.csvConfig)(asData));
        },
        error: (err) => {
          console.log(err);
        },
      });
    }

  _sortData(d1: Song, d2: Song, sorting: string[], dirs: string[]): number {
    // @ts-ignore -- need a typesafe way to express these accessor operations, ts-ignore could be a solution
    // if there's not a suitable solution offered by typescript
    if (d1[sorting[0]] > d2[sorting[0]]) {
      return dirs[0] === "asc" ? 1 : -1;
      // @ts-ignore
    } else if (d1[sorting[0]] < d2[sorting[0]]) {
      return dirs[0] === "asc" ? -1 : 1;
    } else {
      if (sorting.length > 1) {
        sorting = sorting.slice(1, sorting.length);
        dirs = dirs.slice(1, dirs.length);
        return this._sortData(d1, d2, sorting, dirs);
      } else {
        return 0;
      }
    }
  }

  sortTableData(initData: SongPlayback[], sortings: string[], dirs: string[]) {
    const tempData = Object.assign([], initData);
    let result: SongPlayback[] = [];
    if (sortings.length === 0) {
      result = initData;
    } else if (sortings.length > 0) {
      const sortedData = tempData.sort((u1, u2) => {
        return this._sortData(u1, u2, sortings, dirs);
      });
      result = sortedData;
    }
    return result;
  }

  getArtistReproductions(searchParams: ArtistSearchParams) {
    this.artistService.getArtistReproductions(searchParams)
      .subscribe((res) => {
        this.refreshTableData(res.body as ArtistReproductions);
      });
  }

  initTableData() {
    this.table.dataSource = new MatMultiSortTableDataSource<SongPlayback>(
      this.sort,
      false
    );
    this.table.sortObservable.subscribe(() => {
      this.table.data = this.sortTableData(
        this.baseData,
        this.table.sortParams,
        this.table.sortDirs
      );
    });
    this.getArtistReproductions(this.getSearchParams());
  }

  onPageChange(e: PageEvent) {
    this.table.totalElements = e.length;
    this.table.pageIndex = e.pageIndex;
    this.table.pageSize = e.pageSize;
    this.getArtistReproductions(this.getSearchParams());
  }

  ngOnInit() {
    this.reproductionsSearchForm.valueChanges
      .pipe(debounceTime(500))
      .subscribe(() => {
        this.getArtistReproductions(this.getSearchParams());
      });
    this.changeDetectorRef.detectChanges();
    this.initTableData();
  }
}
