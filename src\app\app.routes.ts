import { Routes } from '@angular/router';
import authChildGuard from './guards/auth-child.guard';
import { userLoggedGuard } from './guards/user-logged.guard';

export const routes: Routes = [
  {
    path: 'login',
    loadChildren: () => import('./sections/login/login.module').then((m) => m.LoginModule),
    canActivateChild:[userLoggedGuard]
  },
  {
    path: 'dashboard',
    loadChildren: () =>
      import('./sections/dashboard/dashboard.module').then((m) => m.DashboardModule),
    canActivateChild:[authChildGuard]
  },
  {
    path: '**',
    redirectTo: '/login',
    pathMatch: 'full',
  },
];
