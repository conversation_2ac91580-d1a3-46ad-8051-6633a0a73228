import { HttpClient, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

// Definición de las interfaces dentro del mismo archivo del servicio
interface Artista {
  id: number;
  nombre: string;
  estatus: string;
  fechacreacion: string;
}

interface Album {
  id: number;
  nombre: string;
  estatus: string;
  urlcaratula: string;
  fechacreacion: string;
  idartista: Artista;
}

interface Cancion {
  id: number;
  nombre: string;
  idalbum: Album | null;
}

interface RespuestaBusqueda {
  httpCode: number;
  systemMessage: string;
  songs: Cancion[];
  artists: any[];
}

// Definición de constantes para los códigos de estado HTTP
const HTTP_STATUS_BAD_REQUEST = 400;
const HTTP_STATUS_UNAUTHORIZED = 401;
const HTTP_STATUS_FORBIDDEN = 403;
const HTTP_STATUS_NOT_FOUND = 404;
const HTTP_STATUS_INTERNAL_SERVER_ERROR = 500;

@Injectable({
  providedIn: 'root'
})
export class BusquedaService {
  private url = `${environment.apiUrl}/search/all/`;

  constructor(private http: HttpClient) { }

  buscar(cadena: string): Observable<HttpResponse<RespuestaBusqueda>> {
    return this.http.get<RespuestaBusqueda>(this.url + cadena, { observe: 'response' }).pipe(
        catchError(error => {
            let errorMessage = 'Ha ocurrido un error desconocido';
          
            if (error.error instanceof ErrorEvent) {
              // Errores del lado del cliente o de la red.
              errorMessage = `Error: ${error.error.message}`;
            } else {
              // Errores del lado del servidor.
              switch (error.status) {
                case HTTP_STATUS_BAD_REQUEST:
                  errorMessage = 'Solicitud incorrecta. Por favor, verifica los datos de la búsqueda.';
                  break;
                case HTTP_STATUS_UNAUTHORIZED:
                  errorMessage = 'No autorizado. Por favor, verifica tus credenciales.';
                  break;
                case HTTP_STATUS_FORBIDDEN:
                  errorMessage = 'Prohibido. No tienes permiso para realizar esta acción.';
                  break;
                case HTTP_STATUS_NOT_FOUND:
                  errorMessage = 'No se encontró el recurso solicitado.';
                  break;
                case HTTP_STATUS_INTERNAL_SERVER_ERROR:
                  errorMessage = 'Error interno del servidor. Por favor, intenta más tarde.';
                  break;
                default:
                  errorMessage = `Error del servidor: código ${error.status}, mensaje: ${error.message}`;
                  break;
              }
            }
          
            console.error(errorMessage);
            return throwError(() => errorMessage);
         })
    );
  }
}
