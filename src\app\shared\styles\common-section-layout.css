:host {
    flex: 1;
    display: flex;
    padding: 56px 0px 24px 81px;
    box-sizing: border-box;
    flex-direction: column;
    gap: 40px;
    overflow-y: auto;
}

.section-header,
.section-content {
    display: flex;
    max-width: var(--asMaxWidth,959px);
    margin: 0px auto;
}

.section-header {
    width: 100%;
    align-items: center;
    height: fit-content;
    gap: 16px;

    p {
        font-family: 'Visby CF';
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        color: #fff;
        line-height: 20px;
        margin: 0px;
    }
}

.section-content {
    width: 100%;
    flex-direction: column;
}