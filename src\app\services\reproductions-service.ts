import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import {
  ReproductionByPeriod,
  SearchReproductionsComplex,
  SearchReproductionsSimple,
  SongReproductionsDTO,
} from '../types/reproductions';
import { CARRIERS } from '../types/api';

@Injectable({
  providedIn: 'root',
})
export class ReproductionsService {
  constructor(private http: HttpClient) {}

  private apiUrl = environment.apiUrl;
  private serviceUrl = `${this.apiUrl}/search`;
  private playbackUrl = `${this.apiUrl}/playback-history`

  getReproductionsData(searchParams: SearchReproductionsSimple) {
    const nParams = new HttpParams({fromObject:(searchParams as { [p:string]:any})});
    return this.http.get<SongReproductionsDTO>(`${this.serviceUrl}/reproductions`,{ observe:"response", responseType:"json", params:nParams});
  }

  getReproductionsDataWithFilters(searchParams: SearchReproductionsComplex) {
    return this.http.post<SongReproductionsDTO>(`${this.serviceUrl}/reproductions/complex`,searchParams,{ observe:"response", responseType:"json"});
  }

  getReproductionsByMonth(searchParams:{startDate:string,endDate:string,carrier:CARRIERS|null}) {
    const nParams = new HttpParams({fromObject:(searchParams as { [p:string]:any})});
    return this.http.get<ReproductionByPeriod[]>(`${this.playbackUrl}/grouped`,{ observe:"response", responseType:"json", params:nParams});
  }

  getReproductionsByMonthV2(searchParams:{startDate:string,endDate:string,carrier:CARRIERS|null,graph:{periods:string[],schema:string} | null}) {
    return this.http.post<ReproductionByPeriod[]>(`${this.playbackUrl}/grouped`,searchParams,{ observe:"response", responseType:"json",});
  }
}
