table {
    width: 100%;
  }
  
  .mat-mdc-form-field {
    font-size: 14px;
    width: 100%;
  }
  
  
  td {
    color: #000;
    font-family: "Visby CF";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */
    background: #FAFAFA;
    gap: 32px;
    padding: 8px 23px;
  }
  
  th {
    color: var(--violet, #550DC5);
    font-family: "Visby CF";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 142.857% */
    height: 69px;
    padding: 8px 23px;
    gap: 32px;
    flex-shrink: 0;
  }

  .boton-editar {
    border-radius: 12px;
    background: #00E5EF;
    box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.18);
    color: #000;
    text-align: center;
    font-family: "Visby CF";
    font-size: 16px;
    font-style: normal;
    font-weight: 900;
    line-height: normal;
    padding: 12px 24px;
    border: 2px solid #00E5EF;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    cursor: pointer; /* Cambia el icono del mouse a una manita */
  }
  
  .boton-editar:hover, .boton-editar:active {
    background: #04bdc0; /* Cambia el color de fondo */
    border-color: #04bdc0; /* Cambia el color de fondo a negro cuando se pasa sobre el botón o se selecciona */
    color: #000; /* Cambia el color del texto a #00E5EF cuando se pasa sobre el botón o se selecciona */
  }
  

  


  /* ID Column */
  td.mat-column-playlist_id,
  th.mat-column-playlist_id {
      width: 51px;
      height: 20px;
  }

  /* Progress Column */
  td.mat-column-name,
  th.mat-column-name {
      width: 200px;
      height: 60px;
      text-align: left;
      padding: 8px 23px;
  }

  /* Name Column */
  td.mat-column-descripcion,
  th.mat-column-descripcion {
      width: 480px;
      height: 60px;
      text-align: left;
      padding: 8px 63px;
  }

  /* Name Botones */
  td.mat-column-botones,
  th.mat-column-botones {
      width: 93px;
      height: 49px;
  }


  div.mat-elevation-z8 {
    padding-left: 45px;
    box-sizing: border-box;
    background: transparent;
    border: none;
    box-shadow: none;
  }

  .message{
   padding-left: 200px;
  }




  
  
  

  
  
  
  