import { Song } from "./reproductions";

export interface Album {
    id: number;
    nombre: string;
    estatus: string
    urlcaratula: string;
    fechacreacion: string;
    idgenero: Gender | null;
    idartista: Artist;
    songs:Partial<Song>[];
}

interface Gender {
    id: number;
    nombre: string;
    estatus: string;
}

interface Artist {
    id: number;
    nombre: string;
    estatus: string;
    fechacreacion: string;
}