.songs-autocomplete-item {
    display: flex;
    align-items: center;
    gap: 16px;
}

.songs-autocomplete-item-text {
    display: flex;
    flex-direction: column;
}

.songs-autocomplete-item-text>h5,
h6 {
    margin: 0;
    font-family: "Visby CF";
    letter-spacing: 0em;
    text-align: left;
    line-height: normal;
    font-weight: 500;
}

.songs-autocomplete-item-text>h5 {
    font-size: 14px;
}

.songs-autocomplete-item-text>h6 {
    font-size: 10px;
}

.search-dialog {
    padding: 0px;
    width: 304px;
}

.search-form {
    width: 100%;
    display: flex;
    flex-direction: column;
}
::ng-deep .search-options-ac {
    --mat-autocomplete-container-shape:12px;
    --mat-autocomplete-background-color:#f2f2f2;
}

::ng-deep .mat-mdc-dialog-surface:has(app-multitarget-search-dialog) {
    --mdc-dialog-container-shape: 12px;
    --mdc-dialog-container-color: #F2F2F2;
}

::ng-deep .mat-mdc-dialog-surface:has(app-multitarget-search-dialog > .search-positive) {
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
}