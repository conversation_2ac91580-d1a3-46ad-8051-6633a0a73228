:host {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

p {
    color: var(--White, #FFF);
    font-feature-settings: 'cv11' on, 'cv01' on, 'ss01' on;
    font-family: "Visby CF";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 142.857% */
    padding: var(--4, 60px) var(--8, 115px);
  }
  
  .bannerlist-container {
    display: flex;
    justify-content: space-between;
    align-items: center; /* Alinea verticalmente los elementos hijos en el centro */  
  }
  
  .bannerlist-button {
    border-radius: 12px;
    background: #00E5EF;
    width: 149px;
    height: 49px;
    font-family: "Visby CF";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
    box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.18);
    margin-right: 70px;
    border: 2px solid #00E5EF;
    color: #000; /* Color original del texto */
    transition: all 0.3s ease; /* Agrega una transición suave */
    cursor: pointer; /* Cambia el cursor a una mano cuando pasa por encima */
  }
  
  /* Cuando el mouse pasa por encima del botón */
  .bannerlist-button:hover {
    background: #119b9e; /* Cambia el color de fondo */
    border-color: #119b9e; /* Cambia el color del borde */
    color: #000; /* Cambia el color del texto */
    box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.3); /* Aumenta la sombra */
  }
  
  
  
  