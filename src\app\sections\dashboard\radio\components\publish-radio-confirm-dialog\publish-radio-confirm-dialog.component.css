@import '../../../../../shared/styles/buttons.css';

::ng-deep .publish-radio-dialog-container {
    --mat-dialog-container-max-width: 722px;
    --mat-dialog-container-min-width: 722px;
    --mat-dialog-with-actions-content-padding: 40px 56px 0px 56px;
    --mat-dialog-actions-padding: 0px 56px 56px 56px;
    --mdc-dialog-container-color: #1A191F;
}

.as-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 56px;
    color: #FFF;
    border-bottom: 1px solid rgba(199, 202, 207, 0.50);
    padding: 8px 0px;

    h4 {
        font-size: 24px;
        font-weight: 700;
        font-style: normal;
        line-height: normal;
        margin: 0px;
        font-family: "Visby CF";
    }

    mat-icon {
        font-size: 40px;
        width: 40px;
        height: 40px;
        font-weight: 100;
        cursor: pointer;
    }
}

.common-flat-button {
    --mdc-filled-button-label-text-tracking:0.14px;
    margin: 0px !important;
}

.plain-button {
    --mdc-filled-button-container-color: #E0E2E7
}

.as-description {
    font-family: "Visby CF";
    font-weight: 600;
    margin: 0px;
    margin-bottom: 56px;
    gap: 8px;
    color: white;

    p {
        text-align: center;
        margin: 0px;
    }
}

.as-actions {
    justify-content: center;
    gap: 24px;

    button {
        flex: 1;
    }
}