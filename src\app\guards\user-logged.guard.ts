import { CanActivateChildFn } from '@angular/router';
import { UserProfileService } from '../services/user-profile-service';
import { RoutingService } from '../services/routing-service';
import { CookieService } from 'ngx-cookie-service';
import { inject } from '@angular/core';
import { jwtCookieKey } from '../../constants/jwt';
import { map } from 'rxjs';

export const userLoggedGuard: CanActivateChildFn = (route, state) => {
  const userProfileService = inject(UserProfileService);
  const routingService = inject(RoutingService);
  const cookieService = inject(CookieService);
  return userProfileService.getProfile().pipe(
    map((res) => {
      if (res !== undefined && cookieService.get(jwtCookieKey) !== null){
        routingService.add('/dashboard');
        return false;
      };
      return true;
    })
  );
};
