import { Component } from '@angular/core';
import { RoutingService } from '../../../services/routing-service';

@Component({
  selector: 'app-banner-list',
  templateUrl: './banner-list.component.html',
  styleUrl: './banner-list.component.css'
})
export class BannerListComponent {

  isButtonClicked = false;
  texto = 'Banners';
  mostrarActualizarBannerlist = false;
  bannerIdToEdit: string = '';

  constructor(private routingService: RoutingService) {}
  
  invokeNavigateTo(url: string) {
    this.routingService.add(url);
  }

  onCreateClick() { 
    this.invokeNavigateTo('dashboard/banner-list/new');
  }

  onEditarClicked(event: {editar: boolean, bannerId: string}) {
    /*if (event.editar) {
      this.isButtonClicked = false;
      this.mostrarActualizarBannerlist = true;
      this.bannerIdToEdit = event.bannerId; // guarda el ID de la lista de reproducción
    }*/
  }
}

