import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { SearchResults } from '../types/search';
import { Song } from '../types/reproductions';
import { SongType } from '../types/song';

@Injectable({
    providedIn: 'root'
})
export class SearchService {
    constructor(private http: HttpClient) {}

    quickSearch: string[] = [];
    
    private apiUrl = environment.apiUrl;
    private serviceUrl = `${this.apiUrl}/search`;

    addQuickSearch(search:string) {
        this.quickSearch.push(search);
    }

    getQuickSearch() {
        const temp = this.quickSearch;
        this.quickSearch = [];
        return temp;
    }

    searchByName(name:string) {
        return this.http.get<SearchResults>(`${this.serviceUrl}/${name}`,{ observe:"response", responseType:"json"});
    }

    searchByNameV2(name:string) {
        return this.http.get<SongType[]>(`${this.serviceUrl}/songs-lookup`,{ params:{s:name},observe:"response", responseType:"json"});
    }
}
