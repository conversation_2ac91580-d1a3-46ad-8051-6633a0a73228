<style>
  :host {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .main-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-left: 85px;
  }

  .utilities-container {
    width: 82.5%;
    margin-top: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .data-container {
    width: 100%;
    margin-top: 32px;
    display: flex;
    flex: 1;
    padding-bottom: 48px;
  }

  .data-table-container {
    width: 82.5%;
  }

  .data-actions-container {
    width: 17.5%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
  }

  /**/
  table ::ng-deep th {
    font-family: "Visby CF";
    font-size: 12px;
    font-weight: 600;
    line-height: 20px;
    letter-spacing: 0em;
    text-align: left;
    color: #550dc5;
  }

  table ::ng-deep td {
    font-family: "Visby CF";
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    text-align: left;
    color: #201F28;
  }

  .reproductions-column {
    font-weight: 700;
    color: #df08a8;
  }

  .custom-sort {
    width: 16px;
    height: 16px;
    margin-left: 6px;
  }

  .custom-sort-inverse {
    transform: rotate(180deg);
  }

  ::ng-deep .table-settings-sort {
    display: none;
  }


  ::ng-deep .mat-sort-header-arrow {
    display: none !important;
  }

  ::ng-deep .mat-sort-header-container :last-child.ng-star-inserted {
    color: white;
  }


  /**/

  /**/
  .custom-date-picker {
    cursor: pointer;
    --as-border-color: #ffffff9e;
    --mdc-outlined-text-field-input-text-color: white;
    --mat-form-field-leading-icon-color: #ffffff9e;
    --mat-form-field-container-vertical-padding: 6px;
    --mat-form-field-container-height: 16px;
    --mat-form-field-container-text-weight: 700;
    --mat-form-field-container-text-size: 12px;
    --mat-form-field-container-text-line-height: 18px;
    --mdc-icon-button-icon-color: #FFFFFF9E;
    --mdc-outlined-text-field-hover-outline-color: var(--as-border-color);
    --mdc-outlined-text-field-outline-color: var(--as-border-color);
    --mdc-outlined-text-field-focus-outline-color: var(--as-border-color);
    --mdc-outlined-text-field-caret-color: white;
    --mdc-outlined-text-field-input-text-placeholder-color: white;
    --mdc-outlined-text-field-container-shape: 12px;
    width: 144px;
  }

  .custom-date-picker-icon {
    padding: 0px 2px 0px 8px !important;
    font-size: 16px;
    width: 16px;
    height: 16px;
  }

  .custom-date-picker-input {
    text-align: center;
  }

  
  .expand-datepicker:hover {
    width: 152px;
    transition: width 500ms ease-in-out;
  }

  .expand-datepicker-input {
    margin-left: 0px;
    transition: margin-left 1000ms ease-in-out;
  }

  .expand-datepicker-input:hover {
    margin-left: 4px;
    transition: margin-left 1000ms ease-in-out;
  }

  .custom-date-picker-toggle-icon {
    padding: 0px 8px 0px 2px !important;
    font-size: 16px;
    width: 16px;
    height: 16px;
  }

  .custom-date-picker-toggle-icon ::ng-deep .mat-mdc-icon-button {
    --mdc-icon-button-state-layer-size: 16px;
    --mdc-icon-button-icon-size: 16px;
    padding: 0px;
  }

  .custom-date-picker-toggle-icon ::ng-deep .mat-mdc-button-touch-target {
    width: var(--mdc-icon-button-state-layer-size);
    height: var(--mdc-icon-button-state-layer-size);
  }

  .custom-date-picker-header {
    font-family: "Visby CF";
    font-size: 10px;
    font-weight: 600;
    line-height: 18px;
    letter-spacing: 0em;
    text-align: left;
    color: white;
    margin-bottom: 3px;
  }

  .custom-date-picker-header:has(+.expand-datepicker:hover) {
    margin-left:8px;
    transition: margin-left 500ms ease-in-out;
  }

  .custom-date-picker-apply-btn {
    color: white !important;
    background-color: #550dc5 !important;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 700;
    font-family: "Visby CF";
    line-height: 22px;
  }
  /**/

  /**/
  .filter-menu-button {
    display: flex;
    align-items: center;
    border: 1px solid #FFFFFF9E;
    border-radius: 8px;
    padding: 4px 8px;
  }

  .filter-menu-button ::ng-deep .mat-icon {
    color: white;
    fill: #FFFFFF9E;
    font-size: 16px;
    width: 16px;
    height: 16px;
  }

  .filter-option-rb {
    --rb-color: #550DC5;
    --mdc-radio-selected-icon-color: var(--rb-color);
    --mdc-radio-selected-focus-icon-color: var(--rb-color);
    --mdc-radio-selected-hover-icon-color: var(--rb-color);
    --mdc-radio-selected-pressed-icon-color: var(--rb-color);
    --mat-radio-checked-ripple-color: var(--rb-color);
    --mdc-radio-state-layer-size: 40px;
    --radio-width: 2px;
  }

  .mat-mdc-radio-checked {
    --radio-width: 6px !important;
  }

  .filter-option-rb ::ng-deep .mat-mdc-radio-touch-target {
    width: 20px;
    height: 20px;
  }

  .filter-option-rb ::ng-deep .mdc-radio__outer-circle {
    border-width: var(--radio-width, 2px);
  }

  .filter-option-rb ::ng-deep .mdc-radio__inner-circle {
    border-color: white !important;
    width: 4px;
    height: 4px;
    border-width: 0px;
  }

  .filter-menu-text {
    font-family: "Visby CF";
    font-size: 12px;
    font-weight: 700;
    line-height: 18px;
    letter-spacing: 0em;
    text-align: center;
    color: white;
    margin: 0px 4px;
  }

  .filter-menu-header {
    font-family: "Visby CF";
    font-size: 12px;
    font-weight: 500;
    line-height: 19px;
    letter-spacing: 0.05em;
    text-align: left;
    color: #6e6893;
    margin: 0px;
  }

  .filter-menu {
    padding: 12px 27px;
    display: flex;
    flex-direction: column;
    width: 184px;
    height: 147px;
  }

  ::ng-deep .mat-datepicker-content:has(::ng-deep .menu-backdrop) {
    border-radius: 12px;
    padding: 22.5px 39.5px;
    --mat-datepicker-calendar-container-text-color:transparent;
    margin-top: 10px;
  }

  ::ng-deep .mat-calendar-content ::ng-deep button {
    font-weight: 600;
  }

  ::ng-deep .menu-backdrop {
    --mat-menu-container-shape: 12px;
    --mat-datepicker-calendar-date-selected-state-background-color: #550DC5;
    --mat-datepicker-calendar-header-text-color: #550DC5;
    --mat-datepicker-calendar-header-text-weight: 600;
    --mat-datepicker-calendar-header-text-size:15px;
    --mat-datepicker-calendar-text-font:"Visby CF";
    margin-top: 10px;
  }

  ::ng-deep .mat-calendar-table-header-divider {
    display: none;
  }

  ::ng-deep .menu-backdrop-shadow {
    background-color: rgba(0, 0, 0, 0.25) !important;
  }

  .filter-menu-options {
    display: flex;
    flex-direction: column;
  }

  /**/

  .filter-applied-option {
    color: white;
    background-color: #DF08A8;
    text-align: center;
    font-feature-settings: 'cv11' on, 'cv01' on, 'ss01' on;
    font-family: "Visby CF";
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    line-height: 18px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    padding: 4px 8px;
    gap: 6px;
  }

  .filter-applied-option>p {
    margin-bottom: 0px;
  }

  .filter-applied-option-cancel {
    --mdc-icon-button-state-layer-size: 20px;
    padding: 0px 0px;
  }

  .filter-applied-option-cancel ::ng-deep .mat-icon {
    width: 20px;
    height: 20px;
    font-size: 16px;
  }

  .filter-applied-option-cancel ::ng-deep .mat-mdc-button-touch-target {
    width: var(--mdc-icon-button-state-layer-size);
    height: var(--mdc-icon-button-state-layer-size);
  }

  .filter-clear-button {
    --mdc-text-button-label-text-color: white;
    font-family: "Visby CF";
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    line-height: 18px;
    text-decoration-line: underline;
  }

  /**/
  .search-display-button {
    width: 304px;display: flex;gap: 8px;
    border-radius: 8px;
    background-color: #ffffff40;
    border: none;
    color: white;
    padding: 5px 0px;
  }

  .search-display-icon {
    height: 20px;
    width: 20px;
    font-size: 20px;
    padding: 0px 8px 0px 8px;
  }

  .search-display-button-text {
    font-size: 12px;
    line-height: 20px;
    font-family: "Visby CF";
    font-weight: 600;
    margin: 0px;
    flex: 1;
    text-align: start;
  }

  /**/

  @media screen and (min-width: 1440px) {
    .utilities-container {
      width: 78%;
    }

    .data-table-container {
      width: 78%;
    }

    .data-actions-container {
      width: 22%;
    }
  }

  @media screen and (min-height: 1020px) {
    .overview-container {
      height: 100%;
    }

    .utilities-container {
      margin-top: 28px;
    }
  }

  /**/
  
  .non-opacity-background {
    opacity: 0 !important;
  }
</style>
<div class="main-container">
  <form class="utilities-container" [formGroup]="reproductionsSearchForm">
    <div style="display: flex; align-self: flex-end; gap: 21px; align-items: center;">
      <div>
        <div class="filter-menu-button" [matMenuTriggerFor]="menu" style="cursor: pointer">
          <mat-icon matPrefix svgIcon="filter_icon">filter_alt</mat-icon>
          <p class="filter-menu-text">Filtros</p>
          <mat-icon matSuffix style="color: #FFFFFF9E;">expand_more</mat-icon>
        </div>
        <mat-menu #menu="matMenu" hasBackdrop="true" class="menu-backdrop" backdropClass="menu-backdrop-shadow">
          <mat-radio-group aria-label="Select an option" class="filter-menu filter-menu-options"
            [formControl]="reproductionsSearchForm.controls.carrierOption">
            <p class="filter-menu-header">Suscriptores</p>
            @for (option of carrierOptions; track option) {
            <div style="display: flex;align-items: center;flex: auto;justify-content: space-between;">
              <p style="margin: 0px;">{{option.label}}</p>
              <mat-radio-button [value]="option.value" labelPosition="before" disableRipple="true"
                class="filter-option-rb"></mat-radio-button>
            </div>
            }
          </mat-radio-group>
        </mat-menu>
      </div>
      <button (click)="openSearchDialog()" #searchDisplayButton class="search-display-button">
        <mat-icon class="search-display-icon">search</mat-icon>
        <p class="search-display-button-text">Buscar artista, álbum, canción....</p>
        <mat-icon class="search-display-icon">expand_more</mat-icon>
      </button>
    </div>
    <div style="display: flex; align-self: flex-end; gap: 17px;">
      <div style="display: flex; flex-direction: column">
        <p class="custom-date-picker-header">Desde</p>
        <mat-form-field floatLabel="always" subscriptSizing="dynamic" appearance="outline" class="custom-date-picker expand-datepicker">
          <input matInput [matDatepicker]="datepicker" [formControl]="reproductionsSearchForm.controls.initDate"
            readonly [max]="reproductionsSearchForm.controls.limitDate.value !== null?reproductionsSearchForm.controls.limitDate.value:maxDate" [disabled]="true" class="custom-date-picker-input" />
          <mat-icon matPrefix class="custom-date-picker-icon">event</mat-icon>
          <mat-datepicker-toggle matIconSuffix [for]="datepicker" class="custom-date-picker-toggle-icon">
            <mat-icon matDatepickerToggleIcon class="custom-date-picker-toggle-icon">
              keyboard_arrow_down
            </mat-icon>
          </mat-datepicker-toggle>
          <mat-datepicker panelClass="menu-backdrop" #datepicker [calendarHeaderComponent]="customHeader">
            <mat-datepicker-actions>
              <button mat-button matDatepickerCancel style="border-radius: 12px">
                Cancelar
              </button>
              <button mat-raised-button matDatepickerApply class="custom-date-picker-apply-btn">
                Aceptar
              </button>
            </mat-datepicker-actions>
          </mat-datepicker>
        </mat-form-field>
      </div>
      <div style="display: flex; flex-direction: column">
        <p class="custom-date-picker-header">Hasta</p>
        <mat-form-field floatLabel="always" subscriptSizing="dynamic" appearance="outline" class="custom-date-picker expand-margin-datepicker">
          <input matInput [matDatepicker]="datepickerv" [formControl]="reproductionsSearchForm.controls.limitDate"
            readonly [min]="reproductionsSearchForm.controls.initDate.value" [max]="maxDate" placeholder=" -- / -- / -- " class="custom-date-picker-input expand-datepicker-input"
            [style]="reproductionsSearchForm.controls.limitDate.value === null ? 'letter-spacing: 2.5px;':''" />
          <mat-icon matPrefix class="custom-date-picker-icon">event</mat-icon>
          <mat-datepicker-toggle matIconSuffix [for]="datepickerv" class="custom-date-picker-toggle-icon">
            <mat-icon matDatepickerToggleIcon class="custom-date-picker-toggle-icon">keyboard_arrow_down
            </mat-icon>
          </mat-datepicker-toggle>
          <mat-datepicker panelClass="menu-backdrop" #datepickerv disabled="false"
            [calendarHeaderComponent]="customHeader">
            <mat-datepicker-actions>
              <button mat-button matDatepickerCancel>Cancelar</button>
              <button mat-raised-button color="primary" matDatepickerApply class="custom-date-picker-apply-btn">
                Aceptar
              </button>
            </mat-datepicker-actions>
          </mat-datepicker>
        </mat-form-field>
      </div>
    </div>
    <div style="display: flex; align-self: flex-end; gap: 17px;width: 100%;margin-top: 24px;flex-wrap: wrap;">
      @for (filterValue of usedFilters; track filterValue;let index = $index) {
      <div class="filter-applied-option">
        <p>
          {{ filterValue }}
        </p>
        <button mat-icon-button iconPositionEnd (click)="removeFilterValue(index)" class="filter-applied-option-cancel">
          <mat-icon>
            close
          </mat-icon>
        </button>
      </div>
      }
      <button *ngIf="usedFilters.length > 0" mat-button class="filter-clear-button" (click)="clearFilters()">Eliminar
        filtros
      </button>
    </div>
  </form>
  <div class="data-container">
    <div class="data-table-container">
      <mat-card style="background-color: #f7f9fb; border-radius: 12px">
      <table mat-table [dataSource]="table.dataSource" matMultiSort (matSortChange)="table.onSortEvent()"
      style="background-color: var(--table-background-color);border-radius: 12px;">
        <ng-container matColumnDef="artist">
          <th mat-header-cell *matHeaderCellDef mat-multi-sort-header="artist">
            Artista
            <mat-icon *ngIf="isSortingActive('artist')" svgIcon="chevron_icon" [class]="getSortingDirection('artist') === 'asc' ? 'custom-sort custom-sort-inverse':'custom-sort'"></mat-icon>
          </th>
          <td mat-cell *matCellDef="let row">{{ row.artist }}</td>
        </ng-container>
        <ng-container matColumnDef="song">
          <th mat-header-cell *matHeaderCellDef mat-multi-sort-header="song">
            Tema
            <mat-icon *ngIf="isSortingActive('song')" svgIcon="chevron_icon" [class]="getSortingDirection('song') === 'asc' ? 'custom-sort custom-sort-inverse':'custom-sort'"></mat-icon>
          </th>
          <td mat-cell *matCellDef="let row">{{ row.song }}</td>
        </ng-container>
        <ng-container matColumnDef="album">
          <th mat-header-cell *matHeaderCellDef mat-multi-sort-header="album">
            Albúm
            <mat-icon *ngIf="isSortingActive('album')" svgIcon="chevron_icon" [class]="getSortingDirection('album') === 'asc' ? 'custom-sort custom-sort-inverse':'custom-sort'"></mat-icon>
          </th>
          <td mat-cell *matCellDef="let row">
            <div style="display: flex; align-items: center;justify-content: space-between;">
              <span>
                {{ row.album }}
              </span>
              <div>
                <mat-icon svgIcon="music_record_icon" style="height: 16px;width: 16px;"
                (click)="invokeNavigateTo('dashboard/reproductions/album-detail/'+row.album_id)"></mat-icon>
              </div>
            </div>
          </td>
        </ng-container>
        <ng-container matColumnDef="gender">
          <th mat-header-cell *matHeaderCellDef mat-multi-sort-header="gender">
            Género
            <mat-icon *ngIf="isSortingActive('gender')" svgIcon="chevron_icon" [class]="getSortingDirection('gender') === 'asc' ? 'custom-sort custom-sort-inverse':'custom-sort'"></mat-icon>
          </th>
          <td mat-cell *matCellDef="let row">{{ row.gender }}</td>
        </ng-container>
        <ng-container matColumnDef="decade">
          <th mat-header-cell *matHeaderCellDef mat-multi-sort-header="decade">
            Década
            <mat-icon *ngIf="isSortingActive('decade')" svgIcon="chevron_icon" [class]="getSortingDirection('decade') === 'asc' ? 'custom-sort custom-sort-inverse':'custom-sort'"></mat-icon>
          </th>
          <td mat-cell *matCellDef="let row">{{ parseDecade(row.date,'YYYY') }}</td>
        </ng-container>
        <ng-container matColumnDef="recordLabel">
          <th mat-header-cell *matHeaderCellDef mat-multi-sort-header="recordLabel">
            Disquera
            <mat-icon *ngIf="isSortingActive('recordLabel')" svgIcon="chevron_icon" [class]="getSortingDirection('recordLabel') === 'asc' ? 'custom-sort custom-sort-inverse':'custom-sort'"></mat-icon>
          </th>
          <td mat-cell *matCellDef="let row">{{ row.recordLabel }}</td>
        </ng-container>
        <ng-container matColumnDef="reproductions">
          <th mat-header-cell *matHeaderCellDef mat-multi-sort-header="reproductions">
            Reproducciones
            <mat-icon *ngIf="isSortingActive('reproductions')" svgIcon="chevron_icon" [class]="getSortingDirection('reproductions') === 'asc' ? 'custom-sort custom-sort-inverse':'custom-sort'"></mat-icon>
          </th>
          <td mat-cell *matCellDef="let row" class="reproductions-column">
            <div style="display: flex; align-items: center">
              <mat-icon svgIcon="reproductions_icon" style="margin-right: 7.5px"></mat-icon>
              <div style="display: flex;align-items: center;width: 65px;justify-content: space-between;">
                <span>
                  {{ quantityParser(row.reproductions) }}
                </span>
                <mat-icon [svgIcon]="row.tendency === 1? 'up_tendency_icon': 'down_tendency_icon'" style="width: 16px;height: 16px;"></mat-icon>
              </div>
            </div>
          </td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="table.displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: table.displayedColumns;"></tr>
      </table>
      <mat-paginator [pageSizeOptions]="[5, 10, 20]" aria-label="Select page of songs" itemsPerPageLabel="Filas por página"
        style="background-color: var(--table-background-color);border-top: 1px solid #a8c5da99;" [pageSize]="table.pageSize" [pageIndex]="table.pageIndex"
        [length]="table.totalElements ? table.totalElements : 0" (page)="onPageChange($event)">
      </mat-paginator>
      </mat-card>
    </div>
    <div class="data-actions-container">
      <button mat-fab color="accent" style="margin-bottom: -28px;" (click)="exportData()">
        <mat-icon svgIcon="download_icon"></mat-icon>
      </button>
    </div>
  </div>
</div>