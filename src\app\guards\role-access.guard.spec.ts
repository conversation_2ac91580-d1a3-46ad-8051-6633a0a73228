import { TestBed } from '@angular/core/testing';
import { CanMatchFn } from '@angular/router';

import { roleAccessGuard } from './role-access.guard';

describe('roleAccessGuard', () => {
  const executeGuard: CanMatchFn = (...guardParameters) => 
      TestBed.runInInjectionContext(() => roleAccessGuard(...guardParameters));

  beforeEach(() => {
    TestBed.configureTestingModule({});
  });

  it('should be created', () => {
    expect(executeGuard).toBeTruthy();
  });
});
