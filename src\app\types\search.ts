import { Album } from "./album";

export enum OptionType {
  ARTIST = 0,
  SONG = 1,
  ALBUM = 2,
}
export interface asOption {
  id: number;
  nombre: string;
  as: OptionType;
  matchTag?: string;
}
export interface asArtist extends asOption {}
export interface asAlbum extends asOption {
  idartista: number;
  urlcaratula: string;
}
export interface asSong extends asOption {
  idalbum: Album | null;
}

export interface SearchResults {
  albums: asAlbum[];
  artists: asArtist[];
  songs: asSong[];
}

