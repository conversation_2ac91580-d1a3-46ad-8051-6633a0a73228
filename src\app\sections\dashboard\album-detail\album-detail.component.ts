import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import moment, { Moment } from 'moment';
import 'moment-duration-format';
import { provideMomentDateAdapter } from '@angular/material-moment-adapter';
import { RoutingService } from '../../../services/routing-service';
import { MatMultiSort, MatMultiSortTableDataSource, TableData } from 'ngx-mat-multi-sort';
import { Song } from '../../../types/reproductions';
import { download, generateCsv, mkConfig } from 'export-to-csv';
import { abbreviateNumber } from 'js-abbreviation-number';
import {ActivatedRoute} from '@angular/router';
import { AlbumService } from '../../../services/album-service';
import { Album } from '../../../types/album';
import { CustomCalendarHeaderComponent } from '../../../components/custom-calendar-header/custom-calendar-header.component';

interface AlbumSearchParams {
  albumId:number;
  startDate:string;
  endDate:string;
}

@Component({
  selector: 'app-album-detail',
  templateUrl: './album-detail.component.html',
  styleUrl: './album-detail.component.css',
  providers: [provideMomentDateAdapter({
    parse: {
      dateInput: ['l', 'LL'],
    },
    display: {
      dateInput: 'DD / MM / YYYY',
      monthYearLabel: 'MMM YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'MMMM YYYY',
    },
  }),],
})
export class AlbumDetailComponent implements OnInit {

  csvConfig = mkConfig({
    columnHeaders: [
      { key: 'song', displayLabel: 'Canción' },
      { key: 'duration', displayLabel: 'Duración' },
      { key: 'reproductions', displayLabel: 'Reproducciones' },
    ],
  });

  baseData!: Song[];
  table: TableData<Song>;
  customHeader = CustomCalendarHeaderComponent;
  _albumId:number;
  maxDate: Moment;
  _albumData!: Album; 
  usedFilters: string[] = [];
  @ViewChild(MatMultiSort) sort!: MatMultiSort;

  constructor(
    private routingService: RoutingService,
    private albumService: AlbumService,
    private route:ActivatedRoute,
    private changeDetectorRef: ChangeDetectorRef,
  ) {
    this._albumId = this.route.snapshot.params['albumId'];
    this.maxDate = moment().subtract(1, 'd');
    this.table = new TableData<Song>(
      [
        { id: 'ind', name: '_ind' },
        { id: 'song', name: 'Canción' },
        { id: 'duration', name: 'Duración' },
        { id: 'reproductions', name: 'Reproducciones' },
      ],
      { localStorageKey: 'settingsAlbumDetail' }
    );
  }

  reproductionsSearchForm = new FormGroup({
    initDate: new FormControl<Moment>(
      moment().subtract(1, 'd').subtract(1, 'month')
    ),
    limitDate: new FormControl<Moment | null>(null),
  });

  invokeNavigateTo(url: string) {
    this.routingService.add(url);
  }
  addFilterValue(newFilter: string) {
    this.usedFilters.push(newFilter);
  }
  removeFilterValue(index: number) {
    this.usedFilters = this.usedFilters.filter((e, ind) => ind !== index);
  }
  clearFilters() {
    this.usedFilters = [];
  }
  isSortingActive(sort: string): boolean {
    return this.table.sortParams.includes(sort);
  }
  _sortData(d1: Song, d2: Song, sorting: string[], dirs: string[]): number {
    // @ts-ignore -- need a typesafe way to express these accessor operations, ts-ignore could be a solution
    // if there's not a suitable solution offered by typescript
    if (d1[sorting[0]] > d2[sorting[0]]) {
      return dirs[0] === 'asc' ? 1 : -1;
      // @ts-ignore
    } else if (d1[sorting[0]] < d2[sorting[0]]) {
      return dirs[0] === 'asc' ? -1 : 1;
    } else {
      if (sorting.length > 1) {
        sorting = sorting.slice(1, sorting.length);
        dirs = dirs.slice(1, dirs.length);
        return this._sortData(d1, d2, sorting, dirs);
      } else {
        return 0;
      }
    }
  }
  public parseDecade(d: string, asFormat: string) {
    return moment(d).format(asFormat);
  }
  public parseDuration(d: string) {
    return moment.duration(parseInt(d),'s').format('m:ss');
  }
  getSortingDirection(sort: string): string {
    return this.table.sortDirs[
      this.table.sortParams.findIndex((e) => sort === e)
    ];
  }
  getSearchParams(){
    return {
      startDate:
        this.reproductionsSearchForm.controls.initDate.value?.format(
          'YYYY-MM-DD'
        ),
      endDate:
        this.reproductionsSearchForm.controls.limitDate.value === null
          ? moment().subtract(1, 'd').format('YYYY-MM-DD')
          : this.reproductionsSearchForm.controls.limitDate.value.format(
              'YYYY-MM-DD'
            ),
      albumId:this._albumId,
    };
  }
  quantityParser(qty: string) {
    return abbreviateNumber(parseInt(qty), 1);
  }
  exportData() {
    const data = this.sortTableData(this.baseData,this.table.sortParams,this.table.sortDirs);
    const asData: any = data.map(({duration,...r}) => ({...r,duration:this.parseDuration(duration as string)}));
    download({...this.csvConfig,filename:`REPRODUCCIONES_AQUSTICO_ALBUM_${this._albumId}`})(generateCsv(this.csvConfig)(asData));
  }

  getReproductionsData({albumId,startDate,endDate}:AlbumSearchParams) {
    this.albumService.getAlbumReproductions(albumId,startDate,endDate).subscribe({
      next: (res) => {
        const data = (res.body as Song[]);
        this.table.totalElements = data.length;
        this.baseData = data;
        this.table.data = this.sortTableData(this.baseData,this.table.sortParams,this.table.sortDirs);;
      },
      error: (err) => {
        //console.log(err);
      },
    });
  }

  ngOnInit() {
    this.reproductionsSearchForm.valueChanges.subscribe(() => {
      this.getReproductionsData(this.getSearchParams() as AlbumSearchParams);
    });
    this.changeDetectorRef.detectChanges();
    this.initTableData();
  }

  sortTableData(initData:Song[],sortings:string[],dirs:string[]) {
    const tempData = Object.assign([], initData);
      let result: Song[] = [];
      if (sortings.length === 0) {
        result = initData;
      } else if (sortings.length > 0) {
        const sortedData = tempData.sort((u1, u2) => {
          return this._sortData(u1, u2, sortings, dirs);
        });
        result = sortedData;
      }
      return result;
  }

  initTableData(){
    this.table.dataSource = new MatMultiSortTableDataSource<Song>(this.sort,false);
    this.table.sortObservable.subscribe(() => {
      this.table.data = this.sortTableData(this.baseData,this.table.sortParams,this.table.sortDirs);
    });
    this.albumService.getAlbum(this._albumId).subscribe({
      next:(res) => {
        this._albumData = res.body as Album;
      },
      error: (err) => {
        //console.log(err);
      },
    });
    this.getReproductionsData(this.getSearchParams() as AlbumSearchParams);
  }

}
