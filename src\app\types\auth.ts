import { RecordLabelType } from "./record-label";

export enum Roles {
    COMMERCIAL = 'COMMERCIAL',
    ADMIN = 'ADMIN',
    ARTIST = 'ARTIST',
    RECORD_LABEL = 'RECORD_LABEL',
}

export interface User {
    email: string;
    password: string;
    role: Roles;
    record_label: RecordLabelType;
}
export interface AuthToken {
    access_token:string;
}

export interface AUTH_USER extends User,AuthToken {}