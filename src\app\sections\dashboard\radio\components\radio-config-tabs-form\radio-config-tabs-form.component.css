@import '../../../../../shared/styles/buttons.css';

mat-tab-group {
    --mat-tab-header-inactive-label-text-color: white;
    --mat-tab-header-active-label-text-color: #1A191F;
    --mdc-tab-indicator-active-indicator-height: 0px;
    --mat-tab-header-active-ripple-color: #1a1b1f;
    --mat-tab-header-inactive-ripple-color: #1a1b1f;
    --mat-tab-header-inactive-hover-label-text-color: #1A191F;
    --mat-tab-header-inactive-focus-label-text-color: #1A191F;
    --mat-tab-header-active-focus-label-text-color: #1A191F;
    --mat-tab-header-active-hover-label-text-color: #1A191F;
    ::ng-deep .mat-mdc-tab-labels {
        padding: 16px 0px;
        gap: 10px;
    }
    ::ng-deep .mat-mdc-tab {
        --as-bg: rgba(255, 255, 255, 0.22);
        background: var(--as-bg);
        border-radius: 8px;
        padding: 0px 16px;
        --mdc-secondary-navigation-tab-container-height: 40px;
    }
    ::ng-deep .mdc-tab--active.mdc-tab-indicator--active {
        --as-bg: #1BE2ED;
    }
}

.common-flat-button {
    --mdc-filled-button-label-text-font:"Visby SemiBold CF";
    --mdc-filled-button-label-text-tracking: 0.14px;
}

.radio-config-tabs-content {
    padding: 16px 0px;
}

.tab-header {
    display: flex;
    gap: 8px;
    align-items: center;
    font-family: "Visby CF";
    font-weight: 600;
    font-size: 16px;
    letter-spacing: normal;
}

.tab-content {
    display: flex;
    padding: 24px 0px;

    &.advanced {
        flex-wrap: wrap;
        gap: 24px;
        h4 {
            font-size: 14px;
            color: #C7CACF !important;
            line-height: 16px !important;
            letter-spacing: 1px;
        }
    }

    h3,
    h4 {
        color: #FFF;
        font-family: "Visby Semibold";
        font-style: normal;
        line-height: normal;
        font-weight: 700;
        width: 100%;
        margin: 0px;
    }

    h3 {
        font-size: 24px;
        line-height: 36px !important;
    }

    h4 {
        font-size: 16px;
        letter-spacing: 1px;
        line-height: 16px;
    }
}

.radio-identifier {
    width: 100%;
    display: flex;
    flex-direction: column;
}

.fields-container {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    flex-direction: column;
    flex: 1;
}

.radio-cover-field {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    padding: 6px;
    border-radius: 8px;
    transition: background 0.5s ease-in-out;

    .radio-cover-preview {
        border-radius: 8px;
        padding: 38px 39px;
        background: linear-gradient(137deg, #1C1468 1.26%, #3728CE 69.5%);
        width: 162px;
        height: 84px;
        transition: background 0.5s ease-in-out;
        position: relative;
        display: flex;

        mat-icon {
            margin: auto;
            color: #FFF;
            z-index: 10;
            position: absolute;
            left: calc(50% - 12px);
            top: calc(50% - 12px);
            transition: opacity 0.5s ease-in-out;
        }

        img {
            z-index: 10;
            position: absolute;
            top: 0px;
            left: 0px;
            width: 100%;
            height: 100%;
            border-radius: inherit;
        }

        &::before {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            right: 0;
            content: "";
            border-radius: inherit;
            position: absolute;
            background: linear-gradient(0deg, rgba(0, 0, 0, 0.52) 0%, rgba(0, 0, 0, 0.52) 100%), linear-gradient(137deg, #1C1468 1.26%, #3728CE 69.5%);
            opacity: 0;
            transition: opacity 0.5s ease;
        }
    }

    h5,
    h6 {
        color: #FFF;
        text-align: center;
        font-family: "Visby CF";
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        margin: 0px;
    }

    h6 {
        font-size: 14px !important;
        font-weight: 500 !important;
    }

    &:hover {
        background: rgba(255, 255, 255, 0.10);

        .radio-cover-preview {
            &::before {
                opacity: 1;
            }
        }
    }

    &:hover> ::ng-deep .radio-cover-preview {
        &::before {
            opacity: 1;
        }
    }
}

.radio-info-field-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 4px;

    p {
        margin: 0px;
        color: #FFF;
        font-family: "Visby CF";
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
    }
    
    .radio-info-field-title {
        display: flex;
        gap: 4px;
        > p:nth-child(2) {
            font-style: oblique;
        }
    }

    .radio-info-field {
        width: 100%;
        height: 50px;
        background-color: rgba(255, 255, 255, 0.15);
        border-radius: 12px;
        --mdc-outlined-text-field-input-text-color: white;
        --mat-form-field-container-text-size: 14px;
        --mat-form-field-container-text-line-height: 22px;
        --mat-form-field-container-height: 22px;
        --mat-form-field-container-text-weight: 700;
        --mat-form-field-container-vertical-padding: 14px;
        --as-border-color: transparent;
        --as-border-color-error: #ff003f;
        --mdc-outlined-text-field-hover-outline-color: var(--as-border-color);
        --mdc-outlined-text-field-outline-color: var(--as-border-color);
        --mdc-outlined-text-field-focus-outline-color: var(--as-border-color);

        --mdc-outlined-text-field-caret-color: white;
        --mdc-outlined-text-field-error-caret-color: white;

        border-radius: 12px;
        background-color: #ffffff1f;
        --mdc-outlined-text-field-container-shape: 12px;
        --mdc-outlined-text-field-input-text-placeholder-color: rgb(255, 255, 255, 0.5);

        --mdc-outlined-text-field-error-outline-color: var(--as-border-color-error);

        --mdc-outlined-text-field-error-focus-outline-color: var(--as-border-color-error);

        --mdc-outlined-text-field-error-hover-outline-color: var(--as-border-color-error);

        --mdc-outlined-text-field-outline-width: 2px;

        --mat-form-field-container-text-tracking: 0.14px;

        font-family: Visby CF;
        color: white;

        input::placeholder {
            font-weight: 500;
        }
    }
}


.radio-config-selector-container {
    display: flex;
    flex: 1;
}

.lookup-field-commons {
    --common-color: #E0E2E7;
    --mat-form-field-container-text-font: "Visby CF";
    --mat-form-field-container-text-line-height: 20px;
    --mat-form-field-container-text-size: 16px;
    --mat-form-field-container-text-weight: 600;
    --mdc-filled-text-field-container-color: transparent;
    --mdc-filled-text-field-input-text-color: var(--common-color);
    --mdc-icon-button-icon-color: var(--common-color);
    --mdc-filled-text-field-caret-color: var(--common-color);
    --mdc-filled-text-field-input-text-placeholder-color: var(--common-color);

    --mdc-filled-text-field-hover-active-indicator-color: var(--common-color);
    --mdc-filled-text-field-focus-active-indicator-color: var(--common-color);
    --mdc-filled-text-field-active-indicator-color: var(--common-color);
    --mdc-filled-text-field-active-indicator-height: 1px;
    --mdc-filled-text-field-focus-active-indicator-height: 1px;
}

.lookup-field-selector {
    --mat-select-trigger-text-font: var(--common-color);
    --mat-select-enabled-trigger-text-color: var(--common-color);
    --mat-select-enabled-arrow-color: var(--common-color);
    --mat-select-focused-arrow-color: var(--common-color);
    border: 1px solid var(--common-color);
    border-bottom: none;
    --mdc-filled-text-field-container-color:rgba(26, 25, 31, 1);

   &.mat-focused {
    --common-color: rgba(27, 226, 237, 1);
    --mat-select-trigger-text-weight: 600;
   }
}


::ng-deep .lookup-panel {
   --mat-select-panel-background-color:rgba(26, 25, 31, 1);
   border: 1px solid rgba(27, 226, 237, 1);
   --mat-option-label-text-color:#FFF;
   --mat-option-label-text-font:"Visby CF";
  --mat-option-label-text-line-height: 20px;
   --mat-option-label-text-size: 16px;
   --mat-option-label-text-weight: 600;
   --mat-option-selected-state-label-text-color: white;
   --mat-option-hover-state-layer-color:rgba(27, 226, 237, 1);
}

::ng-deep .lookup-option {
    transition: background-color 0.5s ease-in-out, color 0.5s ease-in-out;
}

::ng-deep .lookup-option:hover {
   --mat-option-selected-state-label-text-color:black;
   --mat-option-label-text-color: black;
   background-color: rgba(27, 226, 237, 1) !important;
}

.lookup-field-input {
    flex: 1;
}

.tab-header-icon {
    font-variation-settings:
        'FILL' 1,
        'wght' 400,
        'GRAD' 0,
        'opsz' 24
}

.form-actions {
    display: flex;
    align-items: center;
    margin-top: 24px;
    justify-content: flex-end;
    gap: 28px;
}

.lookup-elements {
    width: 100%;
    display: flex;
    padding-bottom: 24px;
    border-bottom: 1px solid #C7CACF;
    max-height: calc(64 * 6px);
    flex-direction: column;
    overflow-y: auto;
    box-sizing: border-box;

    &::-webkit-scrollbar {
        display: none;
    }
}

.lookup-elements-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    padding: 80px 0px;
    color: rgba(224, 226, 231, 1);

    mat-icon {
        width: 72px;
        height: 72px;
        font-size: 72px;
    }

    span {
        width: 72px;
        height: 72px;
        font-size: 72px;
    }


    h5 {
        font-family: "Visby CF";
        font-size: 14px;
        font-weight: 600;
        line-height:25.2px;
        text-align: center;
    }
}

.preview-stage {
    flex-wrap: wrap;
    gap: 24px;
    h3 {
        color: #1BE2ED !important;
    }
}

.preview-container {
    display: flex;
    flex: 1;
    gap: 24px;

    img {
        border-radius: 8px;
    }
}

.preview-summary {
    display: flex;
    flex-direction: column;
    gap: 24px;
    flex: 1;
    h4,
    h5 {
        font-family: "Visby CF";
        font-style: normal;
        margin: 0px;
    }

    h4 {
        font-size: 20px;
        font-weight: 700;
        line-height: 30px !important; 
        color: #FFF;
    }

    h5 {
        font-size: 14px;
        font-weight: 700;
        line-height: 16px;
        letter-spacing: 1px;
        color: #C7CACF;
    }
}

.summary-sub-element {
    display: flex;
    flex-wrap: wrap;

    h4 {
        width: fit-content !important;
    }
}