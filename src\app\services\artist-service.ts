import { Injectable } from "@angular/core";
import { HttpClient, HttpParams } from "@angular/common/http";
import { Observable } from "rxjs";
import { environment } from "../../environments/environment";
import { ArtistSearchParams, ArtistType } from "../types/artist";

@Injectable({
  providedIn: "root",
})
export class ArtistService {
  private apiUrl = environment.apiUrl;
  private serviceUrl = `${this.apiUrl}/artist`;
  private playbackServiceUrl = `${this.apiUrl}/playback-history`;

  constructor(private http: HttpClient) {}

  getArtistByMatch(match: string) {
    return this.http.get<ArtistType[]>(this.serviceUrl, {
      params: { s: match },
      observe: "response",
      responseType: "json",
    });
  }

  getArtistReproductions(
    searchParams: ArtistSearchParams
  ): Observable<any> {
    const nParams = new HttpParams({
      fromObject: searchParams as { [p: string]: any },
    });
    return this.http.get<any>(`${this.playbackServiceUrl}/artist`, {
      params: nParams,
      observe: "response",
    });
  }
}
