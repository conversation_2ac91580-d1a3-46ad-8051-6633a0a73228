p {
    color: var(--White, #FFF);
    font-feature-settings: 'cv11' on, 'cv01' on, 'ss01' on;
    font-family: "Visby CF";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 142.857% */
    display: inline-flex;
    padding: var(--4, 60px) var(--8, 115px);
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
  }

  .file-upload-container {
    position: absolute;
    top: 55%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 630px;
    height: 320px;
    display: flex;
    flex-direction: column; /* Añade esta línea */
    justify-content: center;
    align-items: center;
    border-radius: 12px;
    border: 1px dashed var(--Aqua, #1BE2ED);
    padding: 48px 189.5px 47px 190.5px;
    box-sizing: border-box;
    flex-shrink: 0;
  }
  
  
  .file-upload-label {
    padding: 12px 24px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 12px;
    background: #00E5EF;
    box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.18);
    cursor: pointer;
    color: #000;
    text-align: center;
    font-family: "Visby CF";
    font-size: 14px;
    font-style: normal;
    font-weight: bold;
    line-height: normal;
  }

  .file-upload-container img {
    width: 100px; /* Ajusta esto según el tamaño que quieras */
    height: auto;
    margin-bottom: 10px; /* Añade un margen en la parte inferior para separar la imagen del texto */
  }

  .upload-icon {
    width: 96px;
    height: 96px;
    color: #FFFFFF;
  }

  .upload-texto{
    width: 500px;
    align-content: center;
    color: #FFF;
    font-family: "Visby CF";
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px; /* 125% */
    text-align: center; /* Alinea el texto al centro */
  }

  .espacio {
    margin-top: 20px; /* Ajusta este valor según el espacio que quieras */
  }
  
  
  
  
  
  
  

  
  