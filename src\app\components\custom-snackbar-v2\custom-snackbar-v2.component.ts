import { Component, inject, Inject } from '@angular/core';
import { MAT_SNACK_BAR_DATA, MatSnackBarRef } from '@angular/material/snack-bar';

interface SnackbarData {
  message: string;
  asIcon:string;
}

@Component({
  selector: 'app-custom-snackbar-v2',
  templateUrl: './custom-snackbar-v2.component.html',
  styleUrl: './custom-snackbar-v2.component.css'
})
export class CustomSnackbarV2Component {
  constructor(@Inject(MAT_SNACK_BAR_DATA) public data: SnackbarData) {}
  snackBarRef = inject(MatSnackBarRef);
}
