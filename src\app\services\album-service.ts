import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { Album } from '../types/album';
import { Song } from '../types/reproductions';

@Injectable({
    providedIn: 'root',
})
export class AlbumService {
  constructor(private http:HttpClient){}

  private apiUrl = environment.apiUrl;
  private serviceUrl = `${this.apiUrl}/album`;

  public getAlbum(albumId:number){
  return this.http.get<Album>(`${this.serviceUrl}/${albumId}`,{ observe:"response", responseType:"json"},);
  }

  public getAlbumReproductions(id:number,initDate:string,limitDate:string){
    return this.http.get<Song[]>(`${this.serviceUrl}/reproductions`,{ observe:"response", responseType:"json", params:{id,initDate,limitDate}},);
  }
}