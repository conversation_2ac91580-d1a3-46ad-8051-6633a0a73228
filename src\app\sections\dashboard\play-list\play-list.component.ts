import { Component } from '@angular/core';
import { RoutingService } from '../../../services/routing-service';

@Component({
  selector: 'app-play-list',
  templateUrl: './play-list.component.html',
  styleUrls: ['./play-list.component.css']
})
export class PlayListComponent {
  constructor(private routingService: RoutingService,) {} 
  texto = 'Playlist';

  invokeNavigateTo(url: string) {
    this.routingService.add(url);
  }

  onButtonClick() { 
    this.invokeNavigateTo('dashboard/play-list/new');
  }

  onEditarClicked(event: {editar: boolean, playlistId: string}) {
    this.invokeNavigateTo('dashboard/play-list/update/'+event.playlistId);
  }
}

