export enum PlatformMediaType {
    PLATFORM_CONTENT = 0,
    ARTIST_CLIP = 1,
    ADS = 2,
    WELCOME_PLATFORM_CONTENT = 3,
}

export enum PlatformMediaStatus {
    INACTIVE = "INACTIVE",
    ACTIVE = "ACTIVE",
}

export interface PlatformMedia {
    id: number;
    name: string;
    propietaryBrand?:string;
    propietaryBrandIcon?:string;
    coverUrl: string;
    mobileCoverUrl?: string;
    audioUrl?: string;
    redirectTo?: string;
    mediaType: PlatformMediaType;
    status: PlatformMediaStatus;
    mediaTypeLabel: PlatformMediaTypeLabel;
}

export enum PlatformMediaTypeLabel {
    PLATFORM_CONTENT = 'PLATFORM_CONTENT',
    ADS = 'ADS',
    ARTIST_CLIP = 'ARTIST_CLIP',
    WELCOME_PLATFORM_CONTENT = 'WELCOME_PLATFORM_CONTENT',
}
