import { Component } from '@angular/core';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent {
  constructor(iconRegistry: MatIconRegistry, sanitizer: DomSanitizer){
    iconRegistry.addSvgIcon('aqustico_logo', sanitizer.bypassSecurityTrustResourceUrl('assets/aqustico_logo.svg'));
    iconRegistry.addSvgIcon('footer_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/login_footer_icon.svg'));
    iconRegistry.addSvgIcon('overview_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/overview_icon.svg'));
    iconRegistry.addSvgIcon('reproductions_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/reproductions_icon.svg'));
    iconRegistry.addSvgIcon('reproductions_s_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/reproductions_s_icon.svg'));
    iconRegistry.addSvgIcon('sign_out_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/sign_out_icon.svg'));
    iconRegistry.addSvgIcon('users_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/users_icon.svg'));
    iconRegistry.addSvgIcon('header_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/header_icon.svg'));
    iconRegistry.addSvgIcon('bell_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/bell_icon.svg'));
    iconRegistry.addSvgIcon('up_tendency_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/up_tendency_icon.svg'));
    iconRegistry.addSvgIcon('down_tendency_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/down_tendency_icon.svg'));
    iconRegistry.addSvgIcon('download_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/download_icon.svg'));
    iconRegistry.addSvgIcon('alert_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/warning-circled-outline.svg'));
    iconRegistry.addSvgIcon('filter_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/filter_icon.svg'));
    iconRegistry.addSvgIcon('music_record_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/music_record_icon.svg'));
    iconRegistry.addSvgIcon('chevron_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/chevron_icon.svg'));
    iconRegistry.addSvgIcon('group_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/group_icon.svg'));
    iconRegistry.addSvgIcon('movistar_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/logo-movistar.svg'));
    iconRegistry.addSvgIcon('digitel_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/logo-digitel.svg'));
    iconRegistry.addSvgIcon('black_tendency_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/black_tendency.svg'));
    iconRegistry.addSvgIcon('upload_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/upload.svg')); 
    iconRegistry.addSvgIcon('uploadns_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/uploadns.svg'));
    iconRegistry.addSvgIcon('uploadbtn_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/uploadbtn.svg'));
    iconRegistry.addSvgIcon('cargaarchivo_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/cargaarchivo.svg'));
    iconRegistry.addSvgIcon('loaderarchivo_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/loaderarchivo.svg'));
    iconRegistry.addSvgIcon('checketarchivo_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/checketarchivo.svg'));
    iconRegistry.addSvgIcon('cancelarchivo_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/cancelarchivo.svg'));  
    iconRegistry.addSvgIcon('playlistselect_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/playlist_select.svg'));
    iconRegistry.addSvgIcon('playlistunselect_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/playlist_unselect.svg'));
    iconRegistry.addSvgIcon('notamusical_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/notamusical_icon.svg'));
    iconRegistry.addSvgIcon('list_alt_add_cancion_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/list_alt_add_cancion.svg'));
    iconRegistry.addSvgIcon('lupa_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/lupa_icon.svg'));
    iconRegistry.addSvgIcon('editplaylist_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/editplaylist_icon.svg'));
    iconRegistry.addSvgIcon('close-cancion_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/close-cancion.svg'));
    iconRegistry.addSvgIcon('link-playlist_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/link_playlist.svg'));
    iconRegistry.addSvgIcon('banner_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/banner_icon.svg'));
    iconRegistry.addSvgIcon('banner_placeholder_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/banner_placeholder_icon.svg'));
    iconRegistry.addSvgIcon('music_plus_note_icon', sanitizer.bypassSecurityTrustResourceUrl('assets/music_plus_note_icon.svg'));
  }   
}
