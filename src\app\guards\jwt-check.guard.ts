import { CanActivateChildFn } from '@angular/router';
import { inject } from '@angular/core';
import { JwtService } from '../services/jwt-service';
import { RoutingService } from '../services/routing-service';
import { UserProfileService } from '../services/user-profile-service';

export const jwtCheckGuard: CanActivateChildFn = (route, state) => {
  const jwtService = inject(JwtService);
  const routingService = inject(RoutingService);
  const userProfileService = inject(UserProfileService);
  if (!jwtService.isAuthenticated()) {
    userProfileService.logout();
    routingService.add('/login');
    return false;
  }
  return true;
};

export default jwtCheckGuard;